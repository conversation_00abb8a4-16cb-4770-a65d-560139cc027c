# 🚀 راهنمای API برای فرانت‌کار - پنل مشتری

## 🔗 اطلاعات پایه

**Base URL:** `http://localhost:8000/api`

**Headers مورد نیاز:**
```javascript
{
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'Authorization': 'Bearer YOUR_TOKEN' // برای API های محافظت شده
}
```

---

## 🔐 احراز هویت (Authentication)

### 1. ثبت نام
```http
POST /api/register
```

**Body:**
```json
{
  "name": "علی احمدی",
  "email": "<EMAIL>",
  "phone": "09123456789",
  "password": "password123",
  "password_confirmation": "password123",
  "referral_code": "ABC123" // اختیاری - کد معرف
}
```

**Response موفق:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "علی احمدی",
      "email": "<EMAIL>",
      "phone": "09123456789",
      "role": "customer",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "token": "1|abcdef123456..."
  },
  "message": "ثبت نام با موفقیت انجام شد"
}
```

### 2. ورود
```http
POST /api/login
```

**Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response موفق:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "علی احمدی",
      "email": "<EMAIL>",
      "phone": "09123456789",
      "role": "customer",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "token": "1|abcdef123456..."
  },
  "message": "ورود موفقیت‌آمیز بود"
}
```

### 3. خروج 🔐
```http
POST /api/logout
Authorization: Bearer YOUR_TOKEN
```

**Response:**
```json
{
  "success": true,
  "message": "خروج موفقیت‌آمیز بود"
}
```

### 4. دریافت پروفایل 🔐
```http
GET /api/profile
Authorization: Bearer YOUR_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "name": "علی احمدی",
      "email": "<EMAIL>",
      "phone": "09123456789",
      "role": "customer",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    }
  }
}
```

### 5. به‌روزرسانی پروفایل 🔐
```http
PUT /api/profile
Authorization: Bearer YOUR_TOKEN
```

**Body:**
```json
{
  "name": "علی احمدی جدید",
  "phone": "09987654321"
}
```

---

## 📦 پلن‌ها (Plans)

### 1. دریافت لیست پلن‌های فعال
```http
GET /api/plans
```

**Response:**
```json
{
  "success": true,
  "data": {
    "plans": [
      {
        "id": 1,
        "name": "پلن پایه",
        "type": "اشتراک",
        "description": "پلن پایه برای شروع کار",
        "post_purchase_description": "تبریک! شما با موفقیت پلن پایه را خریداری کردید...",
        "link": "https://panel.example.com/basic",
        "banner": "plan-banners/banner1.jpg",
        "banner_url": "http://localhost:8000/storage/plan-banners/banner1.jpg",
        "price": {
          "amount": 100000,
          "formatted": "100,000 تومان",
          "currency": "تومان"
        },
        "duration": {
          "days": 30,
          "formatted": "1 ماه"
        },
        "features": [
          "دسترسی به پنل کاربری",
          "پشتیبانی 24 ساعته",
          "آپدیت رایگان"
        ],
        "status": "active",
        "sort_order": 1,
        "is_active": true,
        "timestamps": {
          "created_at": "2024-01-01T00:00:00.000000Z",
          "updated_at": "2024-01-01T00:00:00.000000Z"
        }
      }
    ]
  },
  "meta": {
    "total": 3,
    "active_plans": 3
  }
}
```

### 2. دریافت جزئیات یک پلن
```http
GET /api/plans/{id}
```

**مثال:**
```http
GET /api/plans/1
```

**Response:** همان ساختار بالا اما فقط یک پلن

---

## 🛒 خریدها (Purchases)

### 1. دریافت لیست خریدهای من 🔐
```http
GET /api/purchases
Authorization: Bearer YOUR_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "purchases": [
      {
        "id": 1,
        "user_id": 1,
        "plan_id": 1,
        "agent_id": 2,
        "amount": 100000,
        "commission_amount": 15000,
        "status": "completed",
        "payment_method": "online",
        "activated_at": "2024-01-01T10:00:00.000000Z",
        "expires_at": "2024-02-01T10:00:00.000000Z",
        "created_at": "2024-01-01T09:00:00.000000Z",
        "updated_at": "2024-01-01T10:00:00.000000Z",
        "plan": {
          "id": 1,
          "name": "پلن پایه",
          "type": "اشتراک",
          "description": "پلن پایه برای شروع کار",
          "banner_url": "http://localhost:8000/storage/plan-banners/banner1.jpg",
          "price": {
            "amount": 100000,
            "formatted": "100,000 تومان"
          },
          "duration": {
            "days": 30,
            "formatted": "1 ماه"
          }
        },
        "agent": {
          "id": 2,
          "referral_code": "ABC123",
          "user": {
            "id": 3,
            "name": "نماینده احمد",
            "email": "<EMAIL>"
          }
        }
      }
    ]
  }
}
```

### 2. ایجاد خرید جدید 🔐
```http
POST /api/purchases
Authorization: Bearer YOUR_TOKEN
```

**Body:**
```json
{
  "plan_id": 1,
  "payment_method": "online" // online, card, cash
}
```

**نکته مهم:** کد نماینده دیگر نیازی نیست در این مرحله وارد شود، چون قبلاً موقع ثبت نام ذخیره شده است.

**Response برای پرداخت آنلاین:**
```json
{
  "success": true,
  "message": "درخواست پرداخت ایجاد شد",
  "data": {
    "purchase": {
      "id": 2,
      "plan": {
        "id": 1,
        "name": "پلن طلایی",
        "price": 100000
      },
      "amount": 100000,
      "status": "pending",
      "transaction_id": "TXN_1234567890_5678",
      "agent": {
        "name": "نماینده احمد",
        "referral_code": "ABC123"
      }
    },
    "payment_url": "https://gateway.example.com/pay/xyz123",
    "gateway_token": "abc123def456"
  }
}
```

**Response برای سایر روش‌های پرداخت:**
```json
{
  "success": true,
  "message": "خرید با موفقیت انجام شد",
  "data": {
    "purchase": {
      "id": 2,
      "plan": {
        "id": 1,
        "name": "پلن طلایی",
        "price": 100000
      },
      "amount": 100000,
      "status": "completed",
      "expires_at": "2024-02-01T09:00:00.000000Z",
      "transaction_id": "TXN_1234567890_5678",
      "agent": {
        "name": "نماینده احمد",
        "referral_code": "ABC123"
      }
    }
  }
}
```

### 3. دریافت پلن‌های فعال کاربر 🔐
```http
GET /api/my-active-plans
Authorization: Bearer YOUR_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "active_plans": [
      {
        "purchase_id": 5,
        "plan": {
          "id": 1,
          "name": "پلن طلایی",
          "type": "premium",
          "price": 100000,
          "duration_days": 30,
          "features": ["ویژگی 1", "ویژگی 2", "ویژگی 3"],
          "post_purchase_description": "توضیحات پس از خرید...",
          "banner": "http://localhost:8000/storage/plans/banner1.jpg"
        },
        "purchase_details": {
          "amount": 100000,
          "activated_at": "2024-01-02T09:00:00.000000Z",
          "expires_at": "2024-02-01T09:00:00.000000Z",
          "remaining_days": 25,
          "is_active": true,
          "transaction_id": "TXN_1234567890_5678"
        },
        "agent": {
          "name": "نماینده احمد",
          "referral_code": "ABC123"
        }
      }
    ],
    "summary": {
      "total_active_plans": 1,
      "total_spent": 100000,
      "nearest_expiry": "2024-02-01T09:00:00.000000Z",
      "has_active_plans": true
    }
  }
}

---

## 💳 پرداخت (Payment)

### 1. بررسی وضعیت پرداخت
```http
GET /api/payment/status?token=abc123def456
```

**Response:**
```json
{
  "success": true,
  "data": {
    "purchase_id": 2,
    "status": "completed",
    "amount": 100000,
    "plan_name": "پلن طلایی",
    "created_at": "2024-01-02T09:00:00.000000Z",
    "gateway_response": {
      "success": true,
      "data": {
        "TransId": "https://gateway.example.com/pay/xyz123"
      }
    }
  }
}
```

### 2. Callback درگاه پرداخت (برای درگاه)
```http
POST /api/payment/callback
```

**Body (از درگاه ارسال می‌شود):**
```json
{
  "token": "abc123def456",
  "trans_id": "xyz123",
  "status": "success",
  "amount": 100000
}
```

**Response:**
```json
{
  "success": true,
  "message": "پرداخت با موفقیت انجام شد",
  "data": {
    "purchase_id": 2,
    "status": "completed"
  }
}
```

### 3. دریافت جزئیات یک خرید 🔐
```http
GET /api/purchases/{id}
Authorization: Bearer YOUR_TOKEN
```

**مثال:**
```http
GET /api/purchases/1
```

---

## 🔍 وضعیت‌های مختلف

### وضعیت خرید (Purchase Status):
- `pending` - در انتظار پرداخت
- `completed` - تکمیل شده و فعال
- `failed` - ناموفق

### نقش کاربر (User Role):
- `customer` - مشتری
- `agent` - نماینده
- `admin` - مدیر

### وضعیت پلن (Plan Status):
- `active` - فعال
- `inactive` - غیرفعال

---

## ❌ مدیریت خطاها

### خطاهای احتمالی:

**401 Unauthorized:**
```json
{
  "success": false,
  "message": "Unauthenticated."
}
```

**422 Validation Error:**
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "email": ["فیلد ایمیل الزامی است."],
    "password": ["رمز عبور باید حداقل 8 کاراکتر باشد."]
  }
}
```

**404 Not Found:**
```json
{
  "success": false,
  "message": "پلن مورد نظر یافت نشد."
}
```

**500 Server Error:**
```json
{
  "success": false,
  "message": "خطای داخلی سرور."
}
```

---

## 🎯 نکات مهم برای فرانت‌کار:

1. **Token Management:** Token را در localStorage ذخیره کنید
2. **Auto Logout:** در صورت دریافت 401، کاربر را به صفحه لاگین هدایت کنید
3. **Loading States:** برای تمام API calls از loading state استفاده کنید
4. **Error Handling:** خطاها را به صورت مناسب به کاربر نمایش دهید
5. **Validation:** از validation سمت کلاینت استفاده کنید
6. **Responsive:** تمام صفحات باید responsive باشند
7. **RTL Support:** پشتیبانی از راست به چپ برای فارسی

---

## 🔧 مثال کد JavaScript:

```javascript
// تنظیم axios
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// اضافه کردن token به header
api.interceptors.request.use(config => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// مدیریت خطای 401
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// مثال استفاده
const login = async (email, password) => {
  try {
    const response = await api.post('/login', { email, password });
    localStorage.setItem('auth_token', response.data.data.token);
    return response.data;
  } catch (error) {
    throw error.response.data;
  }
};
```

این مستندات کامل است و فرانت‌کار می‌تواند بر اساس آن پنل مشتری را پیاده‌سازی کند! 🚀
