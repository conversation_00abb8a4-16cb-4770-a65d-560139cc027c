# 📚 API Documentation - Plans

## 🔗 Base URL
```
http://your-domain.com/api
```

## 🔐 Authentication
برای endpoint های محافظت شده، از Sanctum token استفاده کنید:
```
Authorization: Bearer YOUR_TOKEN_HERE
```

---

## 📦 Plans API

### 1. دریافت پلن‌های فعال (عمومی)
```http
GET /api/plans
```

**Response:**
```json
{
    "success": true,
    "data": {
        "plans": [
            {
                "id": 1,
                "name": "پلن پایه",
                "type": "اشتراک",
                "description": "پلن پایه برای شروع کار",
                "post_purchase_description": "تبریک! شما با موفقیت پلن پایه را خریداری کردید...",
                "link": "https://panel.example.com/basic",
                "banner": "plan-banners/banner1.jpg",
                "banner_url": "http://your-domain.com/storage/plan-banners/banner1.jpg",
                "price": {
                    "amount": 100000,
                    "formatted": "100,000 تومان",
                    "currency": "تومان"
                },
                "duration": {
                    "days": 30,
                    "formatted": "1 ماه"
                },
                "features": [
                    "دسترسی به پنل کاربری",
                    "پشتیبانی 24 ساعته",
                    "آپدیت رایگان"
                ],
                "status": "active",
                "sort_order": 1,
                "is_active": true,
                "timestamps": {
                    "created_at": "2024-01-01T00:00:00.000000Z",
                    "updated_at": "2024-01-01T00:00:00.000000Z"
                }
            }
        ]
    },
    "meta": {
        "total": 3,
        "active_plans": 3
    }
}
```

### 2. دریافت جزئیات یک پلن
```http
GET /api/plans/{id}
```

**Parameters:**
- `include_stats` (optional): برای دریافت آمار پلن

**Example:**
```http
GET /api/plans/1?include_stats=true
```

### 3. دریافت تمام پلن‌ها (ادمین) 🔐
```http
GET /api/admin/plans
```

**Query Parameters:**
- `status` (optional): `active`, `inactive`
- `type` (optional): نوع پلن
- `search` (optional): جستجو در نام و توضیحات
- `sort_by` (optional): `name`, `price`, `created_at`, `sort_order` (default: `sort_order`)
- `sort_direction` (optional): `asc`, `desc` (default: `asc`)
- `per_page` (optional): تعداد نتایج در هر صفحه (default: 15)

**Example:**
```http
GET /api/admin/plans?status=active&search=پایه&sort_by=price&sort_direction=desc&per_page=10
```

**Response:**
```json
{
    "success": true,
    "data": {
        "plans": [...]
    },
    "meta": {
        "current_page": 1,
        "last_page": 2,
        "per_page": 10,
        "total": 15,
        "from": 1,
        "to": 10
    },
    "links": {
        "first": "http://your-domain.com/api/admin/plans?page=1",
        "last": "http://your-domain.com/api/admin/plans?page=2",
        "prev": null,
        "next": "http://your-domain.com/api/admin/plans?page=2"
    }
}
```

### 4. دریافت آمار پلن 🔐
```http
GET /api/plans/{id}/statistics
```

**Response:**
```json
{
    "success": true,
    "data": {
        "plan": {
            "id": 1,
            "name": "پلن پایه"
        },
        "statistics": {
            "total_purchases": 150,
            "completed_purchases": 140,
            "pending_purchases": 8,
            "failed_purchases": 2,
            "active_subscriptions": 120,
            "expired_subscriptions": 20,
            "total_revenue": 15000000,
            "total_commission": 2250000,
            "monthly_stats": [
                {
                    "month": "2024-01",
                    "month_name": "January 2024",
                    "month_persian": "دی 1402",
                    "purchases_count": 25,
                    "revenue": 2500000,
                    "commission": 375000
                }
            ]
        }
    }
}
```

---

## 🎯 Use Cases

### Frontend Integration
```javascript
// دریافت پلن‌های فعال
const getPlans = async () => {
    const response = await fetch('/api/plans');
    const data = await response.json();
    return data.data.plans;
};

// دریافت جزئیات پلن با آمار
const getPlanDetails = async (planId) => {
    const response = await fetch(`/api/plans/${planId}?include_stats=true`);
    const data = await response.json();
    return data.data.plan;
};

// جستجو در پلن‌ها (ادمین)
const searchPlans = async (query, token) => {
    const response = await fetch(`/api/admin/plans?search=${query}`, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    const data = await response.json();
    return data.data.plans;
};
```

### Mobile App Integration
```dart
// Flutter/Dart example
class PlanService {
    static const String baseUrl = 'http://your-domain.com/api';
    
    static Future<List<Plan>> getPlans() async {
        final response = await http.get(Uri.parse('$baseUrl/plans'));
        final data = json.decode(response.body);
        return (data['data']['plans'] as List)
            .map((plan) => Plan.fromJson(plan))
            .toList();
    }
}
```

---

## 🔍 فیلدهای کامل پلن

| فیلد | نوع | توضیحات |
|------|-----|---------|
| `id` | integer | شناسه یکتا |
| `name` | string | نام پلن |
| `type` | string | نوع پلن (اشتراک، محصول، خدمات و...) |
| `description` | string | توضیحات کوتاه |
| `post_purchase_description` | string | توضیحات بعد از خرید |
| `link` | string | لینک مرتبط |
| `banner` | string | مسیر فایل بنر |
| `banner_url` | string | URL کامل بنر |
| `price.amount` | decimal | قیمت خام |
| `price.formatted` | string | قیمت فرمت شده |
| `duration.days` | integer | مدت زمان به روز |
| `duration.formatted` | string | مدت زمان فرمت شده |
| `features` | array | لیست ویژگی‌ها |
| `status` | string | وضعیت (active/inactive) |
| `sort_order` | integer | ترتیب نمایش |
| `is_active` | boolean | آیا فعال است؟ |

---

## ✅ تغییرات اعمال شده

- ✅ تمام فیلدهای جدید (`type`, `post_purchase_description`, `link`, `banner`) اضافه شدند
- ✅ Resource کلاس برای ساختار بهتر API
- ✅ Endpoint جدید برای ادمین با فیلتر و صفحه‌بندی
- ✅ Endpoint آمار پلن با گزارش ماهانه
- ✅ پشتیبانی از بنر با URL کامل
- ✅ ساختار قیمت و مدت زمان بهبود یافته
- ✅ Meta data برای اطلاعات اضافی

API حالا کاملاً به‌روز و جامع است! 🚀
