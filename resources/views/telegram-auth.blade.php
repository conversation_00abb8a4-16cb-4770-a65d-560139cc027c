<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ورود از طریق تلگرام</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tahoma', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 400px;
            width: 90%;
        }
        
        .logo {
            font-size: 3em;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 1.5em;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .subtitle {
            font-size: 1em;
            margin-bottom: 30px;
            opacity: 0.8;
        }
        
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
        
        .status.error {
            background: rgba(244, 67, 54, 0.3);
            border: 1px solid rgba(244, 67, 54, 0.5);
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .user-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: right;
        }
        
        .user-info img {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            float: right;
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🚀</div>
        <div class="title">خوش آمدید!</div>
        <div class="subtitle">در حال ورود به سیستم...</div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <span>لطفاً صبر کنید...</span>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div id="user-info" class="user-info" style="display: none;"></div>
        
        <div id="actions" style="display: none;">
            <a href="#" id="continue-btn" class="btn">ادامه</a>
            <a href="#" id="retry-btn" class="btn" onclick="location.reload()">تلاش مجدد</a>
        </div>
    </div>

    <script>
        // Initialize Telegram WebApp
        const tg = window.Telegram.WebApp;
        tg.ready();
        tg.expand();

        // Get agent ID from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const agentId = urlParams.get('agent_id');
        
        // Elements
        const loadingEl = document.getElementById('loading');
        const statusEl = document.getElementById('status');
        const userInfoEl = document.getElementById('user-info');
        const actionsEl = document.getElementById('actions');
        const continueBtnEl = document.getElementById('continue-btn');

        // Auto authenticate when page loads
        window.addEventListener('load', function() {
            setTimeout(authenticate, 1000); // Small delay for better UX
        });

        async function authenticate() {
            try {
                // Get user data from Telegram WebApp
                const user = tg.initDataUnsafe?.user;
                
                if (!user) {
                    throw new Error('اطلاعات کاربر تلگرام در دسترس نیست');
                }

                if (!agentId) {
                    throw new Error('شناسه نماینده مشخص نیست');
                }

                // Show user info
                showUserInfo(user);

                // Prepare auth data
                const authData = {
                    id: user.id,
                    first_name: user.first_name,
                    last_name: user.last_name,
                    username: user.username,
                    photo_url: user.photo_url,
                    agent_id: agentId
                };

                // Send auth request
                const response = await fetch('/api/telegram/auth', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify(authData)
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(result.message, result.redirect_url);
                } else {
                    throw new Error(result.message || 'خطا در احراز هویت');
                }

            } catch (error) {
                console.error('Auth error:', error);
                showError(error.message);
            }
        }

        function showUserInfo(user) {
            const fullName = (user.first_name || '') + ' ' + (user.last_name || '');
            const photoHtml = user.photo_url ? `<img src="${user.photo_url}" alt="Profile">` : '';
            
            userInfoEl.innerHTML = `
                ${photoHtml}
                <div>
                    <strong>${fullName.trim()}</strong><br>
                    ${user.username ? '@' + user.username : 'شناسه: ' + user.id}
                </div>
            `;
            userInfoEl.style.display = 'block';
        }

        function showSuccess(message, redirectUrl) {
            loadingEl.style.display = 'none';
            statusEl.className = 'status success';
            statusEl.textContent = '✅ ' + message;
            statusEl.style.display = 'block';
            
            continueBtnEl.href = redirectUrl;
            continueBtnEl.textContent = 'ورود به پنل';
            actionsEl.style.display = 'block';
            
            // Auto redirect after 3 seconds
            setTimeout(() => {
                window.location.href = redirectUrl;
            }, 3000);
        }

        function showError(message) {
            loadingEl.style.display = 'none';
            statusEl.className = 'status error';
            statusEl.textContent = '❌ ' + message;
            statusEl.style.display = 'block';
            actionsEl.style.display = 'block';
        }

        // Handle Telegram WebApp events
        tg.onEvent('mainButtonClicked', function() {
            if (continueBtnEl.href) {
                window.location.href = continueBtnEl.href;
            }
        });

        // Set main button if auth is successful
        function setMainButton(text, url) {
            tg.MainButton.setText(text);
            tg.MainButton.show();
            tg.MainButton.onClick(() => {
                window.location.href = url;
            });
        }
    </script>
    
    <meta name="csrf-token" content="{{ csrf_token() }}">
</body>
</html>
