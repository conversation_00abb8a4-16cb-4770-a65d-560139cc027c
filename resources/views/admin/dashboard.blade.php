@extends('layouts.admin')

@section('title', 'داشبورد مدیریت')

@section('content')
        <h1 class="mb-4">داشبورد مدیریت</h1>
        
        <!-- آمار کلی -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>کل کاربران</h5>
                        <h2>{{ number_format($stats['total_users']) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>کل نمایندگان</h5>
                        <h2>{{ number_format($stats['total_agents']) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>کل فروش</h5>
                        <h2>{{ number_format($stats['total_sales']) }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>کمیسیون معلق</h5>
                        <h2>{{ number_format($stats['pending_commissions']) }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین خریدها -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>آخرین خریدها</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>مشتری</th>
                                        <th>پلن</th>
                                        <th>مبلغ</th>
                                        <th>نماینده</th>
                                        <th>وضعیت</th>
                                        <th>تاریخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recent_purchases as $purchase)
                                    <tr>
                                        <td>{{ $purchase->user->name }}</td>
                                        <td>{{ $purchase->plan->name }}</td>
                                        <td>{{ number_format($purchase->amount) }} تومان</td>
                                        <td>{{ $purchase->agent ? $purchase->agent->user->name : '-' }}</td>
                                        <td>
                                            <span class="badge bg-{{ $purchase->status === 'completed' ? 'success' : 'warning' }}">
                                                {{ $purchase->status === 'completed' ? 'تکمیل شده' : 'در انتظار' }}
                                            </span>
                                        </td>
                                        <td>{{ $purchase->created_at->format('Y/m/d') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>برترین نمایندگان</h5>
                    </div>
                    <div class="card-body">
                        @foreach($top_agents as $agent)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ $agent->user->name }}</strong><br>
                                <small class="text-muted">{{ $agent->referral_code }}</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ number_format($agent->total_sales) }}</strong><br>
                                <small class="text-muted">تومان</small>
                            </div>
                        </div>
                        <hr>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
@endsection
