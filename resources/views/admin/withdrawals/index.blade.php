<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت درخواست‌های تسویه حساب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link active" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت درخواست‌های تسویه حساب</h1>
            <a href="{{ route('admin.withdrawals.export', request()->query()) }}" class="btn btn-success">
                خروجی Excel
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- آمار کلی -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>در انتظار بررسی</h5>
                        <h2>{{ $stats['pending_count'] }}</h2>
                        <small>{{ number_format($stats['pending_amount']) }} تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>تکمیل شده</h5>
                        <h2>{{ $stats['completed_count'] }}</h2>
                        <small>{{ number_format($stats['completed_amount']) }} تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>کریپتو (در انتظار)</h5>
                        <h2>{{ number_format($stats['crypto_pending']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-secondary text-white">
                    <div class="card-body">
                        <h5>بانکی (در انتظار)</h5>
                        <h2>{{ number_format($stats['bank_pending']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>تایید شده</option>
                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>در حال پردازش</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>تکمیل شده</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>رد شده</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="method" class="form-select">
                            <option value="">همه روش‌ها</option>
                            <option value="crypto" {{ request('method') == 'crypto' ? 'selected' : '' }}>کریپتو</option>
                            <option value="bank" {{ request('method') == 'bank' ? 'selected' : '' }}>بانکی</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}" placeholder="از تاریخ">
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}" placeholder="تا تاریخ">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                    </div>
                    <div class="col-md-2">
                        <a href="{{ route('admin.withdrawals.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- عملیات دسته‌ای -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="POST" action="{{ route('admin.withdrawals.bulk-approve') }}" id="bulkForm">
                    @csrf
                    <div class="d-flex align-items-center gap-3">
                        <button type="button" class="btn btn-success" onclick="bulkApprove()">
                            تایید انتخاب شده‌ها
                        </button>
                        <input type="text" name="admin_notes" class="form-control" placeholder="توضیحات (اختیاری)" style="max-width: 300px;">
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست درخواست‌ها -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleAll()">
                                </th>
                                <th>شناسه</th>
                                <th>نماینده</th>
                                <th>مبلغ</th>
                                <th>روش</th>
                                <th>وضعیت</th>
                                <th>تاریخ درخواست</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td>
                                    @if($withdrawal->isPending())
                                        <input type="checkbox" name="withdrawal_ids[]" value="{{ $withdrawal->id }}" class="withdrawal-checkbox">
                                    @endif
                                </td>
                                <td>#{{ $withdrawal->id }}</td>
                                <td>
                                    <strong>{{ $withdrawal->agent->user->name }}</strong><br>
                                    <small class="text-muted">{{ $withdrawal->agent->user->email }}</small>
                                </td>
                                <td>{{ $withdrawal->formatted_amount }}</td>
                                <td>
                                    <span class="badge bg-{{ $withdrawal->method === 'crypto' ? 'warning' : 'info' }}">
                                        {{ $withdrawal->method_in_persian }}
                                    </span>
                                    @if($withdrawal->isCrypto())
                                        <br><small class="text-muted">{{ $withdrawal->crypto_type_name }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $withdrawal->status === 'completed' ? 'success' : 
                                        ($withdrawal->status === 'rejected' ? 'danger' : 
                                        ($withdrawal->status === 'processing' ? 'info' : 'warning')) 
                                    }}">
                                        {{ $withdrawal->status_in_persian }}
                                    </span>
                                </td>
                                <td>{{ $withdrawal->created_at->format('Y/m/d H:i') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.withdrawals.show', $withdrawal) }}" class="btn btn-sm btn-outline-primary">
                                            مشاهده
                                        </a>
                                        
                                        @if($withdrawal->isPending())
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="approveWithdrawal({{ $withdrawal->id }})">
                                                تایید
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="rejectWithdrawal({{ $withdrawal->id }})">
                                                رد
                                            </button>
                                        @elseif($withdrawal->isApproved())
                                            <form method="POST" action="{{ route('admin.withdrawals.processing', $withdrawal) }}" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-info">
                                                    در حال پردازش
                                                </button>
                                            </form>
                                        @endif
                                        
                                        @if(in_array($withdrawal->status, ['approved', 'processing']))
                                            <button type="button" class="btn btn-sm btn-success" 
                                                    onclick="completeWithdrawal({{ $withdrawal->id }})">
                                                تکمیل
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">هیچ درخواستی یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $withdrawals->links() }}
            </div>
        </div>
    </div>

    <!-- Modal for actions -->
    <div class="modal fade" id="actionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="actionForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">توضیحات</label>
                            <textarea class="form-control" id="modal_admin_notes" name="admin_notes" rows="3"></textarea>
                        </div>
                        <div id="transactionHashField" style="display: none;">
                            <label for="transaction_hash" class="form-label">هش تراکنش (برای کریپتو)</label>
                            <input type="text" class="form-control" id="transaction_hash" name="transaction_hash">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-primary" id="submitBtn"></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.withdrawal-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function bulkApprove() {
            const checkedBoxes = document.querySelectorAll('.withdrawal-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('لطفاً حداقل یک درخواست را انتخاب کنید.');
                return;
            }
            
            const form = document.getElementById('bulkForm');
            checkedBoxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'withdrawal_ids[]';
                input.value = checkbox.value;
                form.appendChild(input);
            });
            
            form.submit();
        }

        function approveWithdrawal(id) {
            showModal('تایید درخواست', `/admin/withdrawals/${id}/approve`, 'تایید', false);
        }

        function rejectWithdrawal(id) {
            showModal('رد درخواست', `/admin/withdrawals/${id}/reject`, 'رد کردن', false);
            document.getElementById('modal_admin_notes').required = true;
        }

        function completeWithdrawal(id) {
            showModal('تکمیل درخواست', `/admin/withdrawals/${id}/complete`, 'تکمیل', true);
        }

        function showModal(title, action, submitText, showTransactionHash) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('actionForm').action = action;
            document.getElementById('submitBtn').textContent = submitText;
            document.getElementById('transactionHashField').style.display = showTransactionHash ? 'block' : 'none';
            document.getElementById('modal_admin_notes').required = false;
            
            new bootstrap.Modal(document.getElementById('actionModal')).show();
        }
    </script>
</body>
</html>
