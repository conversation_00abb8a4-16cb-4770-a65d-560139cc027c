<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جزئیات درخواست تسویه حساب #{{ $withdrawal->id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .status-timeline {
            position: relative;
            padding-left: 30px;
        }
        .status-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
        }
        .timeline-item.active::before {
            background: #28a745;
        }
        .timeline-item.current::before {
            background: #ffc107;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link active" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات درخواست تسویه حساب #{{ $withdrawal->id }}</h1>
            <div>
                @if($withdrawal->isPending())
                    <button type="button" class="btn btn-success" onclick="approveWithdrawal()">تایید</button>
                    <button type="button" class="btn btn-danger" onclick="rejectWithdrawal()">رد</button>
                @elseif($withdrawal->isApproved())
                    <form method="POST" action="{{ route('admin.withdrawals.processing', $withdrawal) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-info">در حال پردازش</button>
                    </form>
                @endif
                
                @if(in_array($withdrawal->status, ['approved', 'processing']))
                    <button type="button" class="btn btn-success" onclick="completeWithdrawal()">تکمیل</button>
                @endif
                
                <a href="{{ route('admin.withdrawals.index') }}" class="btn btn-secondary">بازگشت</a>
            </div>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <div class="row">
            <div class="col-md-8">
                <!-- اطلاعات درخواست -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات درخواست</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>شناسه درخواست:</strong></td>
                                        <td>#{{ $withdrawal->id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مبلغ:</strong></td>
                                        <td class="text-success"><strong>{{ $withdrawal->formatted_amount }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>روش پرداخت:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ $withdrawal->method === 'crypto' ? 'warning' : 'info' }}">
                                                {{ $withdrawal->method_in_persian }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>وضعیت:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $withdrawal->status === 'completed' ? 'success' : 
                                                ($withdrawal->status === 'rejected' ? 'danger' : 
                                                ($withdrawal->status === 'processing' ? 'info' : 'warning')) 
                                            }}">
                                                {{ $withdrawal->status_in_persian }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>تاریخ درخواست:</strong></td>
                                        <td>{{ $withdrawal->created_at->format('Y/m/d H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاریخ پردازش:</strong></td>
                                        <td>{{ $withdrawal->processed_at ? $withdrawal->processed_at->format('Y/m/d H:i') : '-' }}</td>
                                    </tr>
                                    @if($withdrawal->transaction_hash)
                                    <tr>
                                        <td><strong>هش تراکنش:</strong></td>
                                        <td>
                                            <small class="font-monospace">{{ $withdrawal->transaction_hash }}</small>
                                        </td>
                                    </tr>
                                    @endif
                                </table>
                            </div>
                        </div>

                        @if($withdrawal->agent_notes)
                        <div class="mt-3">
                            <strong>توضیحات نماینده:</strong>
                            <p class="text-muted">{{ $withdrawal->agent_notes }}</p>
                        </div>
                        @endif

                        @if($withdrawal->admin_notes)
                        <div class="mt-3">
                            <strong>توضیحات ادمین:</strong>
                            <p class="text-muted">{{ $withdrawal->admin_notes }}</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- اطلاعات نماینده -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات نماینده</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>نام:</strong></td>
                                        <td>{{ $withdrawal->agent->user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>ایمیل:</strong></td>
                                        <td>{{ $withdrawal->agent->user->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>کد معرف:</strong></td>
                                        <td><span class="badge bg-info">{{ $withdrawal->agent->referral_code }}</span></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>موجودی کیف پول:</strong></td>
                                        <td>{{ number_format($withdrawal->agent->wallet_balance) }} تومان</td>
                                    </tr>
                                    <tr>
                                        <td><strong>کل کمیسیون:</strong></td>
                                        <td>{{ number_format($withdrawal->agent->total_commission) }} تومان</td>
                                    </tr>
                                    <tr>
                                        <td><strong>کل فروش:</strong></td>
                                        <td>{{ number_format($withdrawal->agent->total_sales) }} تومان</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جزئیات پرداخت -->
                <div class="card">
                    <div class="card-header">
                        <h5>جزئیات پرداخت</h5>
                    </div>
                    <div class="card-body">
                        @if($withdrawal->isCrypto())
                            <h6>اطلاعات کیف پول کریپتو:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>نوع ارز:</strong></td>
                                    <td>{{ $withdrawal->crypto_type_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>شبکه:</strong></td>
                                    <td>{{ $withdrawal->network_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آدرس کیف پول:</strong></td>
                                    <td>
                                        <small class="font-monospace">{{ $withdrawal->crypto_wallet_address }}</small>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $withdrawal->crypto_wallet_address }}')">
                                            کپی
                                        </button>
                                    </td>
                                </tr>
                            </table>
                        @else
                            <h6>اطلاعات حساب بانکی:</h6>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>نام بانک:</strong></td>
                                    <td>{{ $withdrawal->bank_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>صاحب حساب:</strong></td>
                                    <td>{{ $withdrawal->bank_account_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>شماره حساب:</strong></td>
                                    <td>{{ $withdrawal->bank_account_number }}</td>
                                </tr>
                            </table>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- وضعیت درخواست -->
                <div class="card">
                    <div class="card-header">
                        <h5>مراحل پردازش</h5>
                    </div>
                    <div class="card-body">
                        <div class="status-timeline">
                            <div class="timeline-item active">
                                <strong>درخواست ثبت شد</strong>
                                <br><small class="text-muted">{{ $withdrawal->created_at->format('Y/m/d H:i') }}</small>
                            </div>
                            
                            <div class="timeline-item {{ in_array($withdrawal->status, ['approved', 'processing', 'completed']) ? 'active' : ($withdrawal->status === 'pending' ? 'current' : '') }}">
                                <strong>در انتظار بررسی</strong>
                                @if($withdrawal->status === 'pending')
                                    <br><small class="text-warning">نیاز به بررسی ادمین</small>
                                @endif
                            </div>
                            
                            @if($withdrawal->status !== 'rejected')
                            <div class="timeline-item {{ in_array($withdrawal->status, ['approved', 'processing', 'completed']) ? 'active' : ($withdrawal->status === 'approved' ? 'current' : '') }}">
                                <strong>تایید شده</strong>
                                @if($withdrawal->status === 'approved')
                                    <br><small class="text-success">آماده پردازش</small>
                                @endif
                            </div>
                            
                            <div class="timeline-item {{ in_array($withdrawal->status, ['processing', 'completed']) ? 'active' : ($withdrawal->status === 'processing' ? 'current' : '') }}">
                                <strong>در حال پردازش</strong>
                                @if($withdrawal->status === 'processing')
                                    <br><small class="text-info">در حال انجام پرداخت</small>
                                @endif
                            </div>
                            
                            <div class="timeline-item {{ $withdrawal->status === 'completed' ? 'active' : '' }}">
                                <strong>تکمیل شده</strong>
                                @if($withdrawal->status === 'completed')
                                    <br><small class="text-success">{{ $withdrawal->processed_at->format('Y/m/d H:i') }}</small>
                                @endif
                            </div>
                            @else
                            <div class="timeline-item active">
                                <strong>رد شده</strong>
                                <br><small class="text-danger">درخواست رد شد</small>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for actions -->
    <div class="modal fade" id="actionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="actionForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">توضیحات</label>
                            <textarea class="form-control" id="modal_admin_notes" name="admin_notes" rows="3"></textarea>
                        </div>
                        <div id="transactionHashField" style="display: none;">
                            <label for="transaction_hash" class="form-label">هش تراکنش (برای کریپتو)</label>
                            <input type="text" class="form-control" id="transaction_hash" name="transaction_hash">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-primary" id="submitBtn"></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function approveWithdrawal() {
            showModal('تایید درخواست', '{{ route("admin.withdrawals.approve", $withdrawal) }}', 'تایید', false);
        }

        function rejectWithdrawal() {
            showModal('رد درخواست', '{{ route("admin.withdrawals.reject", $withdrawal) }}', 'رد کردن', false);
            document.getElementById('modal_admin_notes').required = true;
        }

        function completeWithdrawal() {
            showModal('تکمیل درخواست', '{{ route("admin.withdrawals.complete", $withdrawal) }}', 'تکمیل', true);
        }

        function showModal(title, action, submitText, showTransactionHash) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('actionForm').action = action;
            document.getElementById('submitBtn').textContent = submitText;
            document.getElementById('transactionHashField').style.display = showTransactionHash ? 'block' : 'none';
            document.getElementById('modal_admin_notes').required = false;
            
            new bootstrap.Modal(document.getElementById('actionModal')).show();
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('آدرس کپی شد!');
            });
        }
    </script>
</body>
</html>
