<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ویرایش نماینده - {{ $agent->user->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link active" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>ویرایش نماینده: {{ $agent->user->name }}</h1>
            <div>
                <a href="{{ route('admin.agents.show', $agent) }}" class="btn btn-info">مشاهده جزئیات</a>
                <a href="{{ route('admin.agents.index') }}" class="btn btn-secondary">بازگشت</a>
            </div>
        </div>

        @if($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('admin.agents.update', $agent) }}">
            @csrf
            @method('PUT')
            
            <div class="row">
                <div class="col-md-8">
                    <!-- اطلاعات کاربری -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>اطلاعات کاربری</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">نام و نام خانوادگی *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ old('name', $agent->user->name) }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">ایمیل *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ old('email', $agent->user->email) }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">شماره تلفن</label>
                                    <input type="text" class="form-control" id="phone" name="phone" 
                                           value="{{ old('phone', $agent->user->phone) }}" placeholder="09123456789">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="commission_rate" class="form-label">درصد کمیسیون *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="commission_rate" name="commission_rate" 
                                               value="{{ old('commission_rate', $agent->commission_rate) }}" min="0" max="100" step="0.01" required>
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                               {{ old('is_active', $agent->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            نماینده فعال است
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اطلاعات بانکی -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>اطلاعات بانکی</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="bank_name" class="form-label">نام بانک</label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                           value="{{ old('bank_name', $agent->bank_name) }}" placeholder="بانک ملی">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="bank_account_name" class="form-label">نام صاحب حساب</label>
                                    <input type="text" class="form-control" id="bank_account_name" name="bank_account_name" 
                                           value="{{ old('bank_account_name', $agent->bank_account_name) }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="bank_account_number" class="form-label">شماره حساب</label>
                                    <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" 
                                           value="{{ old('bank_account_number', $agent->bank_account_number) }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- اطلاعات نماینده -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>اطلاعات نماینده</h5>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>کد معرف:</strong></td>
                                    <td><span class="badge bg-info">{{ $agent->referral_code }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>کل فروش:</strong></td>
                                    <td>{{ number_format($agent->total_sales) }} تومان</td>
                                </tr>
                                <tr>
                                    <td><strong>کل کمیسیون:</strong></td>
                                    <td>{{ number_format($agent->total_commission) }} تومان</td>
                                </tr>
                                <tr>
                                    <td><strong>موجودی کیف پول:</strong></td>
                                    <td>{{ number_format($agent->wallet_balance) }} تومان</td>
                                </tr>
                                <tr>
                                    <td><strong>تاریخ عضویت:</strong></td>
                                    <td>{{ $agent->created_at->format('Y/m/d') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- راهنما -->
                    <div class="card">
                        <div class="card-header">
                            <h5>راهنما</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">🔹 فیلدهای ستاره‌دار اجباری هستند</li>
                                <li class="mb-2">🔹 کد معرف قابل تغییر نیست</li>
                                <li class="mb-2">🔹 تغییر درصد کمیسیون فقط روی خریدهای آینده تأثیر دارد</li>
                                <li class="mb-2">🔹 غیرفعال کردن نماینده مانع از خریدهای جدید می‌شود</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">ذخیره تغییرات</button>
                <a href="{{ route('admin.agents.show', $agent) }}" class="btn btn-secondary btn-lg">انصراف</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
