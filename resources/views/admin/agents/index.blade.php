@extends('layouts.admin')

@section('title', 'مدیریت نمایندگان')

@section('content')
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت نمایندگان</h1>
            <a href="{{ route('admin.agents.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> افزودن نماینده جدید
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="جستجو در نام، ایمیل یا کد معرف" value="{{ request('search') }}">
                    </div>
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>فعال</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>غیرفعال</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                        <a href="{{ route('admin.agents.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست نمایندگان -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>نماینده</th>
                                <th>کد معرف</th>
                                <th>درصد کمیسیون</th>
                                <th>کل فروش</th>
                                <th>کل کمیسیون</th>
                                <th>موجودی کیف پول</th>
                                <th>وضعیت</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($agents as $agent)
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ $agent->user->name }}</strong><br>
                                        <small class="text-muted">{{ $agent->user->email }}</small><br>
                                        @if($agent->user->phone)
                                            <small class="text-muted">{{ $agent->user->phone }}</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ $agent->referral_code }}</span>
                                </td>
                                <td>{{ $agent->commission_rate }}%</td>
                                <td>{{ number_format($agent->total_sales) }} تومان</td>
                                <td>{{ number_format($agent->total_commission) }} تومان</td>
                                <td>{{ number_format($agent->wallet_balance) }} تومان</td>
                                <td>
                                    <span class="badge bg-{{ $agent->is_active ? 'success' : 'danger' }}">
                                        {{ $agent->is_active ? 'فعال' : 'غیرفعال' }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.agents.show', $agent) }}" class="btn btn-sm btn-outline-primary">
                                            مشاهده
                                        </a>
                                        <a href="{{ route('admin.agents.edit', $agent) }}" class="btn btn-sm btn-outline-warning">
                                            ویرایش
                                        </a>
                                        <form method="POST" action="{{ route('admin.agents.destroy', $agent) }}" class="d-inline">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('آیا از حذف این نماینده اطمینان دارید؟')">
                                                حذف
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">هیچ نماینده‌ای یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $agents->links() }}
            </div>
        </div>
@endsection
