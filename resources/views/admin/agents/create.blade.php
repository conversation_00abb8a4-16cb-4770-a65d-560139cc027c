<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>افزودن نماینده جدید</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link active" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>افزودن نماینده جدید</h1>
            <a href="{{ route('admin.agents.index') }}" class="btn btn-secondary">
                بازگشت به لیست
            </a>
        </div>

        @if($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('admin.agents.store') }}">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- اطلاعات کاربری -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>اطلاعات کاربری</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">نام و نام خانوادگی *</label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           value="{{ old('name') }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">ایمیل *</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ old('email') }}" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">شماره تلفن</label>
                                    <input type="text" class="form-control" id="phone" name="phone" 
                                           value="{{ old('phone') }}" placeholder="09123456789">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">رمز عبور *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirmation" class="form-label">تکرار رمز عبور *</label>
                                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="commission_rate" class="form-label">درصد کمیسیون *</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="commission_rate" name="commission_rate" 
                                               value="{{ old('commission_rate', 10) }}" min="0" max="100" step="0.01" required>
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اطلاعات بانکی -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>اطلاعات بانکی</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="bank_name" class="form-label">نام بانک</label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                           value="{{ old('bank_name') }}" placeholder="بانک ملی">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="bank_account_name" class="form-label">نام صاحب حساب</label>
                                    <input type="text" class="form-control" id="bank_account_name" name="bank_account_name" 
                                           value="{{ old('bank_account_name') }}">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="bank_account_number" class="form-label">شماره حساب</label>
                                    <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" 
                                           value="{{ old('bank_account_number') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- راهنما -->
                    <div class="card">
                        <div class="card-header">
                            <h5>راهنما</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">🔹 فیلدهای ستاره‌دار اجباری هستند</li>
                                <li class="mb-2">🔹 کد معرف به صورت خودکار تولید می‌شود</li>
                                <li class="mb-2">🔹 درصد کمیسیون بین 0 تا 100 درصد</li>
                                <li class="mb-2">🔹 اطلاعات بانکی اختیاری است</li>
                                <li class="mb-2">🔹 نماینده می‌تواند بعداً اطلاعات خود را تکمیل کند</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">ایجاد نماینده</button>
                <a href="{{ route('admin.agents.index') }}" class="btn btn-secondary btn-lg">انصراف</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
