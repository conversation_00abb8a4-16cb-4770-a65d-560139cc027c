<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جزئیات نماینده - {{ $agent->user->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link active" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات نماینده: {{ $agent->user->name }}</h1>
            <div>
                <a href="{{ route('admin.agents.edit', $agent) }}" class="btn btn-warning">ویرایش</a>
                <a href="{{ route('admin.agents.index') }}" class="btn btn-secondary">بازگشت</a>
            </div>
        </div>

        <!-- آمار کلی -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>کل فروش</h5>
                        <h2>{{ number_format($stats['total_sales']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>کل کمیسیون</h5>
                        <h2>{{ number_format($stats['total_commission']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>موجودی کیف پول</h5>
                        <h2>{{ number_format($stats['wallet_balance']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>تعداد مشتریان</h5>
                        <h2>{{ number_format($stats['total_customers']) }}</h2>
                        <small>نفر</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <!-- اطلاعات شخصی -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات شخصی</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>نام:</strong></td>
                                <td>{{ $agent->user->name }}</td>
                            </tr>
                            <tr>
                                <td><strong>ایمیل:</strong></td>
                                <td>{{ $agent->user->email }}</td>
                            </tr>
                            <tr>
                                <td><strong>تلفن:</strong></td>
                                <td>{{ $agent->user->phone ?: 'وارد نشده' }}</td>
                            </tr>
                            <tr>
                                <td><strong>کد معرف:</strong></td>
                                <td><span class="badge bg-info">{{ $agent->referral_code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>درصد کمیسیون:</strong></td>
                                <td>{{ $agent->commission_rate }}%</td>
                            </tr>
                            <tr>
                                <td><strong>وضعیت:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $agent->is_active ? 'success' : 'danger' }}">
                                        {{ $agent->is_active ? 'فعال' : 'غیرفعال' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاریخ عضویت:</strong></td>
                                <td>{{ $agent->created_at->format('Y/m/d H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- اطلاعات بانکی -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات بانکی</h5>
                    </div>
                    <div class="card-body">
                        @if($agent->hasBankAccount())
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>نام بانک:</strong></td>
                                    <td>{{ $agent->bank_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>صاحب حساب:</strong></td>
                                    <td>{{ $agent->bank_account_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>شماره حساب:</strong></td>
                                    <td>{{ $agent->bank_account_number }}</td>
                                </tr>
                            </table>
                        @else
                            <div class="alert alert-warning">
                                اطلاعات بانکی تکمیل نشده است.
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <!-- اطلاعات کریپتو -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>کیف پول کریپتو</h5>
                    </div>
                    <div class="card-body">
                        @if($agent->hasCryptoWallet())
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>نوع ارز:</strong></td>
                                    <td>{{ \App\Models\WithdrawalRequest::CRYPTO_TYPES[$agent->crypto_wallet_type] ?? $agent->crypto_wallet_type }}</td>
                                </tr>
                                <tr>
                                    <td><strong>شبکه:</strong></td>
                                    <td>{{ \App\Models\WithdrawalRequest::CRYPTO_NETWORKS[$agent->crypto_network] ?? $agent->crypto_network }}</td>
                                </tr>
                                <tr>
                                    <td><strong>آدرس:</strong></td>
                                    <td>
                                        <small class="font-monospace">
                                            {{ substr($agent->crypto_wallet_address, 0, 15) }}...{{ substr($agent->crypto_wallet_address, -15) }}
                                        </small>
                                    </td>
                                </tr>
                            </table>
                        @else
                            <div class="alert alert-warning">
                                کیف پول کریپتو تنظیم نشده است.
                            </div>
                        @endif
                    </div>
                </div>

                <!-- آمار ماهانه -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>آمار این ماه</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary">{{ number_format($stats['this_month_sales']) }}</h4>
                                <small>فروش (تومان)</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">{{ number_format($stats['this_month_sales'] * $agent->commission_rate / 100) }}</h4>
                                <small>کمیسیون (تومان)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین خریدها -->
        <div class="card">
            <div class="card-header">
                <h5>آخرین خریدها</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>مشتری</th>
                                <th>پلن</th>
                                <th>مبلغ</th>
                                <th>کمیسیون</th>
                                <th>وضعیت</th>
                                <th>تاریخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($agent->purchases()->with(['user', 'plan'])->latest()->limit(10)->get() as $purchase)
                            <tr>
                                <td>{{ $purchase->user->name }}</td>
                                <td>{{ $purchase->plan->name }}</td>
                                <td>{{ number_format($purchase->amount) }} تومان</td>
                                <td>{{ number_format($purchase->commission_amount) }} تومان</td>
                                <td>
                                    <span class="badge bg-{{ $purchase->status === 'completed' ? 'success' : 'warning' }}">
                                        {{ $purchase->status === 'completed' ? 'تکمیل شده' : 'در انتظار' }}
                                    </span>
                                </td>
                                <td>{{ $purchase->created_at->format('Y/m/d') }}</td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="6" class="text-center">هیچ خریدی یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
