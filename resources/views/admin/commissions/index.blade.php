<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت کمیسیون‌ها</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link active" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4">مدیریت کمیسیون‌ها</h1>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- آمار کلی -->
        <div class="row mb-4">
            <div class="col-md-4 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>در انتظار پرداخت</h5>
                        <h2>{{ number_format($stats['pending_amount']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>پرداخت شده</h5>
                        <h2>{{ number_format($stats['paid_amount']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>کل کمیسیون‌ها</h5>
                        <h2>{{ number_format($stats['total_amount']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار</option>
                            <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>پرداخت شده</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>لغو شده</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="agent_id" class="form-select">
                            <option value="">همه نمایندگان</option>
                            @foreach(\App\Models\Agent::with('user')->get() as $agent)
                                <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                    {{ $agent->user->name }} ({{ $agent->referral_code }})
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}" placeholder="از تاریخ">
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}" placeholder="تا تاریخ">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                        <a href="{{ route('admin.commissions.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- عملیات دسته‌ای -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="POST" action="{{ route('admin.commissions.bulk-pay') }}" id="bulkForm">
                    @csrf
                    <div class="d-flex align-items-center gap-3">
                        <button type="button" class="btn btn-success" onclick="bulkPay()">
                            پرداخت انتخاب شده‌ها
                        </button>
                        <input type="text" name="notes" class="form-control" placeholder="توضیحات (اختیاری)" style="max-width: 300px;">
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست کمیسیون‌ها -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleAll()">
                                </th>
                                <th>شناسه</th>
                                <th>نماینده</th>
                                <th>خرید</th>
                                <th>مشتری</th>
                                <th>پلن</th>
                                <th>مبلغ کمیسیون</th>
                                <th>وضعیت</th>
                                <th>تاریخ ایجاد</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($commissions as $commission)
                            <tr>
                                <td>
                                    @if($commission->isPending())
                                        <input type="checkbox" name="commission_ids[]" value="{{ $commission->id }}" class="commission-checkbox">
                                    @endif
                                </td>
                                <td>#{{ $commission->id }}</td>
                                <td>
                                    <div>
                                        <strong>{{ $commission->agent->user->name }}</strong><br>
                                        <small class="text-muted">{{ $commission->agent->referral_code }}</small>
                                    </div>
                                </td>
                                <td>#{{ $commission->purchase_id }}</td>
                                <td>{{ $commission->purchase->user->name }}</td>
                                <td>{{ $commission->purchase->plan->name }}</td>
                                <td>
                                    <strong class="text-success">{{ number_format($commission->amount) }} تومان</strong>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $commission->status === 'paid' ? 'success' : 
                                        ($commission->status === 'cancelled' ? 'danger' : 'warning') 
                                    }}">
                                        {{ 
                                            $commission->status === 'paid' ? 'پرداخت شده' : 
                                            ($commission->status === 'cancelled' ? 'لغو شده' : 'در انتظار') 
                                        }}
                                    </span>
                                </td>
                                <td>{{ $commission->created_at->format('Y/m/d H:i') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.commissions.show', $commission) }}" class="btn btn-sm btn-outline-primary">
                                            مشاهده
                                        </a>
                                        
                                        @if($commission->isPending())
                                            <form method="POST" action="{{ route('admin.commissions.pay', $commission) }}" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-sm btn-success" 
                                                        onclick="return confirm('آیا از پرداخت این کمیسیون اطمینان دارید؟')">
                                                    پرداخت
                                                </button>
                                            </form>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="cancelCommission({{ $commission->id }})">
                                                لغو
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="10" class="text-center">هیچ کمیسیونی یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $commissions->links() }}
            </div>
        </div>
    </div>

    <!-- Modal for cancel commission -->
    <div class="modal fade" id="cancelModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">لغو کمیسیون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="cancelForm" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="cancel_notes" class="form-label">دلیل لغو *</label>
                            <textarea class="form-control" id="cancel_notes" name="notes" rows="3" required></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                        <button type="submit" class="btn btn-danger">لغو کمیسیون</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.commission-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAll.checked;
            });
        }

        function bulkPay() {
            const checkedBoxes = document.querySelectorAll('.commission-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('لطفاً حداقل یک کمیسیون را انتخاب کنید.');
                return;
            }
            
            const form = document.getElementById('bulkForm');
            checkedBoxes.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'commission_ids[]';
                input.value = checkbox.value;
                form.appendChild(input);
            });
            
            form.submit();
        }

        function cancelCommission(id) {
            document.getElementById('cancelForm').action = `/admin/commissions/${id}/cancel`;
            new bootstrap.Modal(document.getElementById('cancelModal')).show();
        }
    </script>
</body>
</html>
