@extends('layouts.admin')

@section('title', 'مدیریت ربات‌های تلگرام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🤖 مدیریت ربات‌های تلگرام</h1>
                <a href="{{ route('admin.telegram-bots.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> افزودن ربات جدید
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">🔍 جستجو و فیلتر</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.telegram-bots.index') }}">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="search" class="form-label">جستجو</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="نام نماینده، ایمیل یا نام کاربری ربات...">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="status" class="form-label">وضعیت</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">همه</option>
                                    <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>فعال</option>
                                    <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>غیرفعال</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> جستجو
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📋 لیست ربات‌های تلگرام</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>نماینده</th>
                                    <th>ربات تلگرام</th>
                                    <th>وضعیت</th>
                                    <th>آمار</th>
                                    <th>آخرین بروزرسانی</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($bots as $bot)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $bot->agent->user->name }}</strong><br>
                                            <small class="text-muted">{{ $bot->agent->user->email }}</small><br>
                                            <span class="badge bg-info">{{ $bot->agent->referral_code }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>@{{ $bot->bot_username ?? 'نامشخص' }}</strong><br>
                                            <small class="text-muted">{{ Str::limit($bot->bot_token, 20) }}</small><br>
                                            @if($bot->bot_username)
                                                <a href="https://t.me/{{ $bot->bot_username }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                    <i class="fab fa-telegram"></i> مشاهده
                                                </a>
                                            @else
                                                <span class="text-muted small">نام کاربری تنظیم نشده</span>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" 
                                                   id="status-{{ $bot->id }}" {{ $bot->is_active ? 'checked' : '' }}
                                                   onchange="toggleBotStatus({{ $bot->id }})">
                                            <label class="form-check-label" for="status-{{ $bot->id }}">
                                                {{ $bot->is_active ? 'فعال' : 'غیرفعال' }}
                                            </label>
                                        </div>
                                        @if($bot->webhook_set_at)
                                            <span class="badge bg-success">webhook فعال</span>
                                        @else
                                            <span class="badge bg-danger">webhook غیرفعال</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <span class="badge bg-primary">{{ $bot->telegramUsers()->count() }} کاربر</span><br>
                                            <span class="badge bg-success">{{ $bot->telegramUsers()->where('is_registered', true)->count() }} ثبت‌نام</span>
                                        </div>
                                    </td>
                                    <td>
                                        {{ $bot->updated_at->format('Y/m/d H:i') }}<br>
                                        <small class="text-muted">{{ $bot->updated_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.telegram-bots.show', $bot) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.telegram-bots.edit', $bot) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" 
                                                    onclick="confirmDelete({{ $bot->id }})">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-secondary"
                                                    onclick="testBot({{ $bot->id }})">
                                                <i class="fas fa-vial"></i>
                                            </button>
                                            @if(app()->environment('local'))
                                                <button type="button" class="btn btn-sm btn-success"
                                                        onclick="simulateWebhook({{ $bot->id }})">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            @endif
                                            <button type="button" class="btn btn-sm btn-primary"
                                                    onclick="resetWebhook({{ $bot->id }})">
                                                <i class="fas fa-sync"></i>
                                            </button>
                                        </div>
                                        <form id="delete-form-{{ $bot->id }}" 
                                              action="{{ route('admin.telegram-bots.destroy', $bot) }}" 
                                              method="POST" style="display: none;">
                                            @csrf
                                            @method('DELETE')
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-robot fa-3x mb-3"></i>
                                            <p>هیچ رباتی یافت نشد.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $bots->withQueryString()->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if($agentsWithoutBots->count() > 0)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">👤 نمایندگان بدون ربات تلگرام</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>نماینده</th>
                                    <th>کد معرف</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($agentsWithoutBots as $agent)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $agent->user->name }}</strong><br>
                                            <small class="text-muted">{{ $agent->user->email }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $agent->referral_code }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.telegram-bots.create', ['agent_id' => $agent->id]) }}" 
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-plus"></i> افزودن ربات
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@push('scripts')
<script>
function confirmDelete(botId) {
    if (confirm('آیا از حذف این ربات اطمینان دارید؟ این عمل غیرقابل بازگشت است.')) {
        document.getElementById('delete-form-' + botId).submit();
    }
}

function toggleBotStatus(botId) {
    fetch('{{ url("admin/telegram-bots") }}/' + botId + '/toggle-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const label = document.querySelector('label[for="status-' + botId + '"]');
            label.textContent = data.is_active ? 'فعال' : 'غیرفعال';
        } else {
            alert('خطا در تغییر وضعیت ربات');
            // Revert the checkbox state
            const checkbox = document.getElementById('status-' + botId);
            checkbox.checked = !checkbox.checked;
        }
    })
    .catch(error => {
        alert('خطا در ارتباط با سرور');
        // Revert the checkbox state
        const checkbox = document.getElementById('status-' + botId);
        checkbox.checked = !checkbox.checked;
    });
}

function testBot(botId) {
    fetch('{{ url("admin/telegram-bots") }}/' + botId + '/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تست ربات');
    });
}

function simulateWebhook(botId) {
    fetch('{{ url("api/telegram/webhook") }}/' + botId + '/simulate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ شبیه‌سازی webhook با موفقیت انجام شد!\n\nپیام /start از کاربر تست ارسال شد.');
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در شبیه‌سازی webhook');
    });
}

function resetWebhook(botId) {
    if (!confirm('آیا از تنظیم مجدد webhook اطمینان دارید؟')) {
        return;
    }

    fetch('{{ url("admin/telegram-bots") }}/' + botId + '/reset-webhook', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تنظیم مجدد webhook');
    });
}
</script>
@endpush
