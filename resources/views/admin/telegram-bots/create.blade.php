@extends('layouts.admin')

@section('title', 'افزودن ربات تلگرام جدید')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🤖 افزودن ربات تلگرام جدید</h1>
                <a href="{{ route('admin.telegram-bots.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت به لیست
                </a>
            </div>
        </div>
    </div>

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">⚙️ اطلاعات ربات تلگرام</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.telegram-bots.store') }}">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="agent_id" class="form-label">انتخاب نماینده <span class="text-danger">*</span></label>
                            <select class="form-select" id="agent_id" name="agent_id" required {{ $agent ? 'disabled' : '' }}>
                                <option value="">-- انتخاب نماینده --</option>
                                @foreach($agents as $agentItem)
                                    <option value="{{ $agentItem->id }}" {{ ($agent && $agent->id == $agentItem->id) || old('agent_id') == $agentItem->id ? 'selected' : '' }}>
                                        {{ $agentItem->user->name }} ({{ $agentItem->user->email }}) - کد: {{ $agentItem->referral_code }}
                                    </option>
                                @endforeach
                            </select>
                            @if($agent)
                                <input type="hidden" name="agent_id" value="{{ $agent->id }}">
                            @endif
                            <div class="form-text">
                                نماینده‌ای که این ربات به آن تعلق خواهد داشت.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bot_token" class="form-label">توکن ربات <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="bot_token" name="bot_token" 
                                   value="{{ old('bot_token') }}" 
                                   placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz" required>
                            <div class="form-text">
                                توکن ربات را از @BotFather در تلگرام دریافت کنید.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bot_username" class="form-label">نام کاربری ربات <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">@</span>
                                <input type="text" class="form-control" id="bot_username" name="bot_username" 
                                       value="{{ old('bot_username') }}" 
                                       placeholder="my_agent_bot" required>
                            </div>
                            <div class="form-text">
                                نام کاربری ربات باید با نام کاربری تنظیم شده در BotFather مطابقت داشته باشد.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="welcome_message" class="form-label">پیام خوشامدگویی</label>
                            <textarea class="form-control" id="welcome_message" name="welcome_message" rows="5"
                                      placeholder="پیام خوشامدگویی سفارشی...">{{ old('welcome_message') }}</textarea>
                            <div class="form-text">
                                پیام خوشامدگویی که هنگام ارسال دستور /start به کاربر نمایش داده می‌شود. در صورت خالی بودن، پیام پیش‌فرض نمایش داده می‌شود.
                            </div>
                        </div>

                        <!-- Mandatory Channel Settings -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">📢 تنظیمات کانال اجباری</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="mandatory_channel_username" class="form-label">نام کاربری کانال</label>
                                    <div class="input-group">
                                        <span class="input-group-text">@</span>
                                        <input type="text" class="form-control" id="mandatory_channel_username" name="mandatory_channel_username"
                                               value="{{ old('mandatory_channel_username') }}"
                                               placeholder="my_channel">
                                    </div>
                                    <div class="form-text">
                                        نام کاربری کانال (بدون @). اگر پر شود، کاربران باید قبل از استفاده از ربات عضو این کانال شوند.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="mandatory_channel_id" class="form-label">شناسه کانال</label>
                                    <input type="text" class="form-control" id="mandatory_channel_id" name="mandatory_channel_id"
                                           value="{{ old('mandatory_channel_id') }}"
                                           placeholder="-1001234567890">
                                    <div class="form-text">
                                        شناسه عددی کانال (مثل -1001234567890). در صورت وجود نام کاربری، این فیلد اختیاری است.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="mandatory_channel_title" class="form-label">عنوان کانال</label>
                                    <input type="text" class="form-control" id="mandatory_channel_title" name="mandatory_channel_title"
                                           value="{{ old('mandatory_channel_title') }}"
                                           placeholder="کانال رسمی ما">
                                    <div class="form-text">
                                        عنوان کانال که در پیام‌های ربات نمایش داده می‌شود.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('admin.telegram-bots.index') }}" class="btn btn-outline-secondary">
                                انصراف
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> ذخیره ربات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📝 راهنمای ایجاد ربات</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">مراحل ایجاد ربات در تلگرام:</h6>
                        <ol class="mb-0">
                            <li>به @BotFather در تلگرام پیام دهید.</li>
                            <li>دستور /newbot را ارسال کنید.</li>
                            <li>نام نمایشی ربات را وارد کنید (مثلاً "ربات نماینده علی").</li>
                            <li>نام کاربری ربات را وارد کنید (باید به bot ختم شود، مثلاً ali_agent_bot).</li>
                            <li>پس از ایجاد ربات، توکن آن را کپی کنید.</li>
                            <li>توکن و نام کاربری را در فرم وارد کنید.</li>
                        </ol>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">نکات مهم:</h6>
                        <ul class="mb-0">
                            <li>هر نماینده فقط می‌تواند یک ربات تلگرام داشته باشد.</li>
                            <li>توکن ربات محرمانه است و نباید در اختیار دیگران قرار گیرد.</li>
                            <li>پس از ایجاد ربات، webhook آن به صورت خودکار تنظیم می‌شود.</li>
                            <li>برای تغییر تنظیمات ربات، می‌توانید بعداً آن را ویرایش کنید.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
