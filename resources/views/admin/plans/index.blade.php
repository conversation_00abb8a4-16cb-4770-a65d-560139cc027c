<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مدیریت پلن‌ها</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .plan-card { transition: transform 0.2s; }
        .plan-card:hover { transform: translateY(-2px); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link active" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>مدیریت پلن‌ها</h1>
            <a href="{{ route('admin.plans.create') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> افزودن پلن جدید
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- نمایش پلن‌ها به صورت کارت -->
        <div class="row">
            @forelse($plans as $plan)
            <div class="col-md-4 mb-4">
                <div class="card plan-card h-100 {{ $plan->status === 'active' ? 'border-success' : 'border-secondary' }}">
                    @if($plan->banner)
                        <img src="{{ $plan->banner_url }}" class="card-img-top" alt="بنر {{ $plan->name }}"
                             style="height: 200px; object-fit: cover;">
                    @endif
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ $plan->name }}</h5>
                        <span class="badge bg-{{ $plan->status === 'active' ? 'success' : 'secondary' }}">
                            {{ $plan->status === 'active' ? 'فعال' : 'غیرفعال' }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h2 class="text-primary">{{ number_format($plan->price) }}</h2>
                            <small class="text-muted">تومان</small>
                        </div>
                        
                        <p class="text-muted">{{ $plan->description }}</p>
                        
                        <div class="mb-3">
                            <strong>مدت زمان:</strong> {{ $plan->formatted_duration }}
                        </div>

                        @if($plan->features)
                            <div class="mb-3">
                                <strong>ویژگی‌ها:</strong>
                                <ul class="list-unstyled mt-2">
                                    @foreach($plan->features as $feature)
                                        <li><i class="text-success">✓</i> {{ $feature }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <div class="row text-center">
                            <div class="col-6">
                                <small class="text-muted">کل خریدها</small>
                                <div class="fw-bold">{{ $plan->purchases()->count() }}</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">درآمد کل</small>
                                <div class="fw-bold">{{ number_format($plan->purchases()->where('status', 'completed')->sum('amount')) }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-group w-100" role="group">
                            <a href="{{ route('admin.plans.show', $plan) }}" class="btn btn-outline-primary btn-sm">
                                مشاهده
                            </a>
                            <a href="{{ route('admin.plans.edit', $plan) }}" class="btn btn-outline-warning btn-sm">
                                ویرایش
                            </a>
                            @if(!$plan->purchases()->exists())
                                <form method="POST" action="{{ route('admin.plans.destroy', $plan) }}" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                            onclick="return confirm('آیا از حذف این پلن اطمینان دارید؟')">
                                        حذف
                                    </button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h4>هیچ پلنی یافت نشد</h4>
                    <p>برای شروع، اولین پلن خود را ایجاد کنید.</p>
                    <a href="{{ route('admin.plans.create') }}" class="btn btn-primary">ایجاد پلن جدید</a>
                </div>
            </div>
            @endforelse
        </div>

        {{ $plans->links() }}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
