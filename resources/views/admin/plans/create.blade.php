<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ایجاد پلن جدید</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .feature-input { margin-bottom: 10px; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link active" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>ایجاد پلن جدید</h1>
            <a href="{{ route('admin.plans.index') }}" class="btn btn-secondary">
                بازگشت به لیست
            </a>
        </div>

        @if($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('admin.plans.store') }}" enctype="multipart/form-data">
            @csrf
            
            <div class="row">
                <div class="col-md-8">
                    <!-- اطلاعات اصلی -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>اطلاعات اصلی پلن</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">نام پلن *</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="{{ old('name') }}" required placeholder="مثال: پلن طلایی">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="type" class="form-label">نوع پلن</label>
                                    <input type="text" class="form-control" id="type" name="type"
                                           value="{{ old('type') }}" placeholder="مثال: اشتراک، محصول، خدمات">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="price" class="form-label">قیمت (تومان) *</label>
                                    <input type="number" class="form-control" id="price" name="price"
                                           value="{{ old('price') }}" required min="0" placeholder="100000">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="duration_days" class="form-label">مدت زمان (روز) *</label>
                                    <select class="form-select" id="duration_days" name="duration_days" required>
                                        <option value="">انتخاب کنید</option>
                                        <option value="30" {{ old('duration_days') == 30 ? 'selected' : '' }}>30 روز (1 ماه)</option>
                                        <option value="90" {{ old('duration_days') == 90 ? 'selected' : '' }}>90 روز (3 ماه)</option>
                                        <option value="180" {{ old('duration_days') == 180 ? 'selected' : '' }}>180 روز (6 ماه)</option>
                                        <option value="365" {{ old('duration_days') == 365 ? 'selected' : '' }}>365 روز (1 سال)</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">وضعیت *</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>فعال</option>
                                        <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>غیرفعال</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="sort_order" class="form-label">ترتیب نمایش</label>
                                    <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                           value="{{ old('sort_order', 0) }}" min="0" placeholder="0">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="description" class="form-label">توضیحات</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"
                                              placeholder="توضیح کوتاهی از پلن...">{{ old('description') }}</textarea>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="post_purchase_description" class="form-label">توضیحات بعد از خرید</label>
                                    <textarea class="form-control" id="post_purchase_description" name="post_purchase_description" rows="3"
                                              placeholder="توضیحاتی که بعد از خرید به مشتری نمایش داده می‌شود...">{{ old('post_purchase_description') }}</textarea>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="link" class="form-label">لینک</label>
                                    <input type="url" class="form-control" id="link" name="link"
                                           value="{{ old('link') }}" placeholder="https://example.com">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="banner" class="form-label">بنر پلن</label>
                                    <input type="file" class="form-control" id="banner" name="banner"
                                           accept="image/*" onchange="previewBanner(this)">
                                    <small class="text-muted">فرمت‌های مجاز: JPG, PNG, GIF - حداکثر 2MB</small>
                                    <div id="banner-preview" class="mt-2" style="display: none;">
                                        <img id="banner-image" src="" alt="پیش‌نمایش بنر" style="max-width: 200px; max-height: 150px; border-radius: 5px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ویژگی‌ها -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>ویژگی‌های پلن</h5>
                        </div>
                        <div class="card-body">
                            <div id="features-container">
                                @if(old('features'))
                                    @foreach(old('features') as $index => $feature)
                                        @if($feature)
                                            <div class="feature-input">
                                                <div class="input-group">
                                                    <input type="text" class="form-control" name="features[]" 
                                                           value="{{ $feature }}" placeholder="ویژگی پلن...">
                                                    <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                                                        حذف
                                                    </button>
                                                </div>
                                            </div>
                                        @endif
                                    @endforeach
                                @else
                                    <div class="feature-input">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="features[]" 
                                                   placeholder="ویژگی پلن...">
                                            <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                                                حذف
                                            </button>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addFeature()">
                                افزودن ویژگی
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- پیش‌نمایش -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>پیش‌نمایش پلن</h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <h4 id="preview-name">نام پلن</h4>
                                <h2 class="text-primary" id="preview-price">0 تومان</h2>
                                <small class="text-muted" id="preview-duration">مدت زمان</small>
                            </div>
                            <p class="text-muted" id="preview-description">توضیحات پلن...</p>
                            <div id="preview-features">
                                <strong>ویژگی‌ها:</strong>
                                <ul id="preview-features-list"></ul>
                            </div>
                        </div>
                    </div>

                    <!-- راهنما -->
                    <div class="card">
                        <div class="card-header">
                            <h5>راهنما</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li class="mb-2">🔹 نام پلن باید منحصربه‌فرد باشد</li>
                                <li class="mb-2">🔹 قیمت به تومان وارد شود</li>
                                <li class="mb-2">🔹 ویژگی‌ها به مشتریان نمایش داده می‌شود</li>
                                <li class="mb-2">🔹 ترتیب نمایش برای مرتب‌سازی پلن‌ها</li>
                                <li class="mb-2">🔹 پلن‌های غیرفعال قابل خرید نیستند</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">ایجاد پلن</button>
                <a href="{{ route('admin.plans.index') }}" class="btn btn-secondary btn-lg">انصراف</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addFeature() {
            const container = document.getElementById('features-container');
            const div = document.createElement('div');
            div.className = 'feature-input';
            div.innerHTML = `
                <div class="input-group">
                    <input type="text" class="form-control" name="features[]" placeholder="ویژگی پلن...">
                    <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                        حذف
                    </button>
                </div>
            `;
            container.appendChild(div);
        }

        function removeFeature(button) {
            button.closest('.feature-input').remove();
            updatePreview();
        }

        function updatePreview() {
            // Update name
            const name = document.getElementById('name').value || 'نام پلن';
            document.getElementById('preview-name').textContent = name;

            // Update price
            const price = document.getElementById('price').value || '0';
            document.getElementById('preview-price').textContent = parseInt(price).toLocaleString() + ' تومان';

            // Update duration
            const duration = document.getElementById('duration_days').value;
            let durationText = 'مدت زمان';
            if (duration == 30) durationText = '1 ماه';
            else if (duration == 90) durationText = '3 ماه';
            else if (duration == 180) durationText = '6 ماه';
            else if (duration == 365) durationText = '1 سال';
            else if (duration) durationText = duration + ' روز';
            document.getElementById('preview-duration').textContent = durationText;

            // Update description
            const description = document.getElementById('description').value || 'توضیحات پلن...';
            document.getElementById('preview-description').textContent = description;

            // Update features
            const features = Array.from(document.querySelectorAll('input[name="features[]"]'))
                .map(input => input.value)
                .filter(value => value.trim() !== '');
            
            const featuresList = document.getElementById('preview-features-list');
            featuresList.innerHTML = '';
            features.forEach(feature => {
                const li = document.createElement('li');
                li.innerHTML = '<i class="text-success">✓</i> ' + feature;
                featuresList.appendChild(li);
            });
        }

        // Add event listeners for real-time preview
        document.addEventListener('DOMContentLoaded', function() {
            ['name', 'price', 'duration_days', 'description'].forEach(id => {
                document.getElementById(id).addEventListener('input', updatePreview);
            });

            document.addEventListener('input', function(e) {
                if (e.target.name === 'features[]') {
                    updatePreview();
                }
            });

            updatePreview();
        });

        function previewBanner(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('banner-image').src = e.target.result;
                    document.getElementById('banner-preview').style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
