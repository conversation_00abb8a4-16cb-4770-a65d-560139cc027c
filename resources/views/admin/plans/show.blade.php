<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جزئیات پلن - {{ $plan->name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .plan-preview {
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">پنل مدیریت</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('admin.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('admin.agents.index') }}">نمایندگان</a>
                <a class="nav-link active" href="{{ route('admin.plans.index') }}">پلن‌ها</a>
                <a class="nav-link" href="{{ route('admin.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('admin.withdrawals.index') }}">درخواست‌های تسویه</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات پلن: {{ $plan->name }}</h1>
            <div>
                <a href="{{ route('admin.plans.edit', $plan) }}" class="btn btn-warning">ویرایش</a>
                <a href="{{ route('admin.plans.index') }}" class="btn btn-secondary">بازگشت</a>
            </div>
        </div>

        <!-- آمار کلی -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>کل خریدها</h5>
                        <h2>{{ $stats['total_purchases'] }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>خریدهای تکمیل شده</h5>
                        <h2>{{ $stats['completed_purchases'] }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>کل درآمد</h5>
                        <h2>{{ number_format($stats['total_revenue']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>اشتراک‌های فعال</h5>
                        <h2>{{ $stats['active_subscriptions'] }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <!-- اطلاعات پلن -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات پلن</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>نام:</strong></td>
                                <td>{{ $plan->name }}</td>
                            </tr>
                            @if($plan->type)
                            <tr>
                                <td><strong>نوع:</strong></td>
                                <td><span class="badge bg-secondary">{{ $plan->type }}</span></td>
                            </tr>
                            @endif
                            <tr>
                                <td><strong>قیمت:</strong></td>
                                <td class="text-success"><strong>{{ $plan->formatted_price }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>مدت زمان:</strong></td>
                                <td>{{ $plan->formatted_duration }}</td>
                            </tr>
                            <tr>
                                <td><strong>وضعیت:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $plan->status === 'active' ? 'success' : 'secondary' }}">
                                        {{ $plan->status === 'active' ? 'فعال' : 'غیرفعال' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>ترتیب نمایش:</strong></td>
                                <td>{{ $plan->sort_order }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاریخ ایجاد:</strong></td>
                                <td>{{ $plan->created_at->format('Y/m/d H:i') }}</td>
                            </tr>
                            <tr>
                                <td><strong>آخرین به‌روزرسانی:</strong></td>
                                <td>{{ $plan->updated_at->format('Y/m/d H:i') }}</td>
                            </tr>
                        </table>

                        @if($plan->description)
                        <div class="mt-3">
                            <strong>توضیحات:</strong>
                            <p class="text-muted">{{ $plan->description }}</p>
                        </div>
                        @endif

                        @if($plan->post_purchase_description)
                        <div class="mt-3">
                            <strong>توضیحات بعد از خرید:</strong>
                            <p class="text-muted">{{ $plan->post_purchase_description }}</p>
                        </div>
                        @endif

                        @if($plan->link)
                        <div class="mt-3">
                            <strong>لینک:</strong>
                            <p><a href="{{ $plan->link }}" target="_blank" class="text-primary">{{ $plan->link }}</a></p>
                        </div>
                        @endif

                        @if($plan->banner)
                        <div class="mt-3">
                            <strong>بنر پلن:</strong>
                            <div class="mt-2">
                                <img src="{{ $plan->banner_url }}" alt="بنر {{ $plan->name }}"
                                     style="max-width: 100%; max-height: 300px; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                            </div>
                        </div>
                        @endif

                        @if($plan->features)
                        <div class="mt-3">
                            <strong>ویژگی‌ها:</strong>
                            <ul class="mt-2">
                                @foreach($plan->features as $feature)
                                    <li>{{ $feature }}</li>
                                @endforeach
                            </ul>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <!-- پیش‌نمایش پلن -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>پیش‌نمایش پلن</h5>
                    </div>
                    <div class="card-body">
                        <div class="plan-preview text-center">
                            @if($plan->banner)
                                <img src="{{ $plan->banner_url }}" alt="بنر {{ $plan->name }}"
                                     style="width: 100%; max-height: 200px; object-fit: cover; border-radius: 10px; margin-bottom: 15px;">
                            @endif

                            <h3>{{ $plan->name }}</h3>
                            @if($plan->type)
                                <span class="badge bg-secondary mb-2">{{ $plan->type }}</span>
                            @endif
                            <h1 class="text-primary">{{ $plan->formatted_price }}</h1>
                            <p class="text-muted">{{ $plan->formatted_duration }}</p>

                            @if($plan->description)
                                <p>{{ $plan->description }}</p>
                            @endif

                            @if($plan->features)
                                <ul class="list-unstyled text-start">
                                    @foreach($plan->features as $feature)
                                        <li><i class="text-success">✓</i> {{ $feature }}</li>
                                    @endforeach
                                </ul>
                            @endif

                            <button class="btn btn-primary btn-lg" disabled>
                                {{ $plan->status === 'active' ? 'خرید پلن' : 'غیرفعال' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین خریدها -->
        <div class="card">
            <div class="card-header">
                <h5>آخرین خریدهای این پلن</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>شناسه</th>
                                <th>مشتری</th>
                                <th>نماینده</th>
                                <th>مبلغ</th>
                                <th>کمیسیون</th>
                                <th>وضعیت</th>
                                <th>تاریخ خرید</th>
                                <th>تاریخ انقضا</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($plan->purchases()->with(['user', 'agent.user'])->latest()->limit(10)->get() as $purchase)
                            <tr>
                                <td>#{{ $purchase->id }}</td>
                                <td>
                                    <div>
                                        <strong>{{ $purchase->user->name }}</strong><br>
                                        <small class="text-muted">{{ $purchase->user->email }}</small>
                                    </div>
                                </td>
                                <td>
                                    @if($purchase->agent)
                                        <div>
                                            <strong>{{ $purchase->agent->user->name }}</strong><br>
                                            <small class="text-muted">{{ $purchase->agent->referral_code }}</small>
                                        </div>
                                    @else
                                        <span class="text-muted">مستقیم</span>
                                    @endif
                                </td>
                                <td>{{ number_format($purchase->amount) }} تومان</td>
                                <td>{{ number_format($purchase->commission_amount) }} تومان</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $purchase->status === 'completed' ? 'success' : 
                                        ($purchase->status === 'failed' ? 'danger' : 'warning') 
                                    }}">
                                        {{ 
                                            $purchase->status === 'completed' ? 'تکمیل شده' : 
                                            ($purchase->status === 'failed' ? 'ناموفق' : 'در انتظار') 
                                        }}
                                    </span>
                                    @if($purchase->isActive())
                                        <br><small class="text-success">فعال</small>
                                    @elseif($purchase->isExpired() && $purchase->isCompleted())
                                        <br><small class="text-danger">منقضی شده</small>
                                    @endif
                                </td>
                                <td>{{ $purchase->created_at->format('Y/m/d H:i') }}</td>
                                <td>
                                    @if($purchase->expires_at)
                                        {{ $purchase->expires_at->format('Y/m/d') }}
                                        @if($purchase->isActive())
                                            <br><small class="text-info">{{ $purchase->remaining_days }} روز باقی</small>
                                        @endif
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="8" class="text-center">هیچ خریدی برای این پلن یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
