@extends('layouts.admin')

@section('title', 'ویرایش خرید #' . $purchase->id)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">ویرایش خرید #{{ $purchase->id }}</h1>
                <div>
                    <a href="{{ route('admin.purchases.show', $purchase) }}" class="btn btn-outline-info">
                        <i class="fas fa-eye"></i> مشاهده
                    </a>
                    <a href="{{ route('admin.purchases.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> بازگشت
                    </a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">ویرایش اطلاعات خرید</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('admin.purchases.update', $purchase) }}">
                                @csrf
                                @method('PUT')

                                <div class="mb-3">
                                    <label for="status" class="form-label">وضعیت <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="pending" {{ $purchase->status === 'pending' ? 'selected' : '' }}>در انتظار</option>
                                        <option value="completed" {{ $purchase->status === 'completed' ? 'selected' : '' }}>تکمیل شده</option>
                                        <option value="failed" {{ $purchase->status === 'failed' ? 'selected' : '' }}>ناموفق</option>
                                        <option value="refunded" {{ $purchase->status === 'refunded' ? 'selected' : '' }}>بازگشت داده شده</option>
                                    </select>
                                    @error('status')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="expires_at" class="form-label">تاریخ انقضا</label>
                                    <input type="datetime-local" class="form-control" id="expires_at" name="expires_at" 
                                           value="{{ $purchase->expires_at ? $purchase->expires_at->format('Y-m-d\TH:i') : '' }}">
                                    @error('expires_at')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        تاریخ و زمان انقضای اشتراک. برای اشتراک نامحدود خالی بگذارید.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">یادداشت</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4" 
                                              placeholder="یادداشت اختیاری...">{{ old('notes', $purchase->notes) }}</textarea>
                                    @error('notes')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{{ route('admin.purchases.show', $purchase) }}" class="btn btn-outline-secondary">
                                        انصراف
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> ذخیره تغییرات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Purchase Info -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">اطلاعات خرید</h6>
                        </div>
                        <div class="card-body">
                            <table class="table table-borderless table-sm">
                                <tr>
                                    <td><strong>شناسه:</strong></td>
                                    <td>#{{ $purchase->id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>کاربر:</strong></td>
                                    <td>{{ $purchase->user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>پلن:</strong></td>
                                    <td>{{ $purchase->plan->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>مبلغ:</strong></td>
                                    <td>{{ number_format($purchase->amount) }} تومان</td>
                                </tr>
                                <tr>
                                    <td><strong>تاریخ ایجاد:</strong></td>
                                    <td>{{ $purchase->created_at->format('Y/m/d H:i') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Current Status -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="card-title mb-0">وضعیت فعلی</h6>
                        </div>
                        <div class="card-body">
                            @php
                                $statusClasses = [
                                    'pending' => 'warning',
                                    'completed' => 'success',
                                    'failed' => 'danger',
                                    'refunded' => 'secondary'
                                ];
                                $statusTexts = [
                                    'pending' => 'در انتظار',
                                    'completed' => 'تکمیل شده',
                                    'failed' => 'ناموفق',
                                    'refunded' => 'بازگشت داده شده'
                                ];
                            @endphp
                            <span class="badge bg-{{ $statusClasses[$purchase->status] ?? 'secondary' }} fs-6">
                                {{ $statusTexts[$purchase->status] ?? $purchase->status }}
                            </span>

                            @if($purchase->isActive())
                                <div class="mt-2">
                                    <small class="text-success">
                                        <i class="fas fa-check-circle"></i> فعال ({{ $purchase->remaining_days }} روز باقی‌مانده)
                                    </small>
                                </div>
                            @elseif($purchase->isExpired() && $purchase->isCompleted())
                                <div class="mt-2">
                                    <small class="text-danger">
                                        <i class="fas fa-times-circle"></i> منقضی شده
                                    </small>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">عملیات سریع</h6>
                        </div>
                        <div class="card-body">
                            <!-- Extend Subscription -->
                            @if($purchase->isCompleted())
                            <form method="POST" action="{{ route('admin.purchases.extend', $purchase) }}" class="mb-3">
                                @csrf
                                <label class="form-label small">تمدید اشتراک:</label>
                                <div class="input-group input-group-sm mb-2">
                                    <input type="number" class="form-control" name="extend_days" 
                                           placeholder="تعداد روز" min="1" max="365" required>
                                    <span class="input-group-text">روز</span>
                                </div>
                                <button type="submit" class="btn btn-info btn-sm w-100">
                                    <i class="fas fa-calendar-plus"></i> تمدید
                                </button>
                            </form>
                            @endif

                            <!-- Toggle Status -->
                            <form method="POST" action="{{ route('admin.purchases.toggle-status', $purchase) }}" class="mb-3">
                                @csrf
                                <button type="submit" class="btn btn-{{ $purchase->status === 'completed' ? 'warning' : 'success' }} btn-sm w-100">
                                    <i class="fas fa-power-off"></i>
                                    {{ $purchase->status === 'completed' ? 'غیرفعال کردن' : 'فعال کردن' }}
                                </button>
                            </form>

                            <!-- Delete -->
                            <form method="POST" action="{{ route('admin.purchases.destroy', $purchase) }}" 
                                  onsubmit="return confirm('آیا از حذف این خرید اطمینان دارید؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger btn-sm w-100">
                                    <i class="fas fa-trash"></i> حذف خرید
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
