@extends('layouts.admin')

@section('title', 'فعال‌سازی پلن جدید')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">فعال‌سازی پلن جدید</h1>
                <a href="{{ route('admin.purchases.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">🎯 فعال‌سازی دستی پلن برای کاربر</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.purchases.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_id" class="form-label">انتخاب کاربر <span class="text-danger">*</span></label>
                                    <select class="form-select" id="user_id" name="user_id" required>
                                        <option value="">-- انتخاب کاربر --</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->id }}" 
                                                {{ (old('user_id') == $user->id || ($selectedUser && $selectedUser->id == $user->id)) ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        کاربری که پلن برای او فعال می‌شود.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="plan_id" class="form-label">انتخاب پلن <span class="text-danger">*</span></label>
                                    <select class="form-select" id="plan_id" name="plan_id" required>
                                        <option value="">-- انتخاب پلن --</option>
                                        @foreach($plans as $plan)
                                            <option value="{{ $plan->id }}" 
                                                data-price="{{ $plan->price }}" 
                                                data-duration="{{ $plan->duration_days }}"
                                                {{ old('plan_id') == $plan->id ? 'selected' : '' }}>
                                                {{ $plan->name }} - {{ number_format($plan->price) }} تومان ({{ $plan->formatted_duration }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('plan_id')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="agent_id" class="form-label">نماینده (اختیاری)</label>
                                    <select class="form-select" id="agent_id" name="agent_id">
                                        <option value="">-- بدون نماینده --</option>
                                        @foreach($agents as $agent)
                                            <option value="{{ $agent->id }}" 
                                                data-commission="{{ $agent->commission_rate }}"
                                                {{ old('agent_id') == $agent->id ? 'selected' : '' }}>
                                                {{ $agent->user->name }} ({{ $agent->referral_code }}) - {{ $agent->commission_rate }}% کمیسیون
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('agent_id')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        نماینده‌ای که کمیسیون این فروش به او تعلق می‌گیرد.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="amount" class="form-label">مبلغ (اختیاری)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="amount" name="amount" 
                                               value="{{ old('amount') }}" min="0" step="1000" placeholder="مبلغ پیش‌فرض پلن">
                                        <span class="input-group-text">تومان</span>
                                    </div>
                                    @error('amount')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        در صورت خالی بودن، قیمت پلن استفاده می‌شود.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">مدت زمان (روز)</label>
                                    <input type="number" class="form-control" id="duration_days" name="duration_days" 
                                           value="{{ old('duration_days') }}" min="1" max="3650" placeholder="مدت پیش‌فرض پلن">
                                    @error('duration_days')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        در صورت خالی بودن، مدت زمان پلن استفاده می‌شود.
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">یادداشت</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="یادداشت اختیاری...">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Summary Card -->
                        <div class="card bg-light mb-3" id="summary-card" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title">خلاصه فعال‌سازی:</h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>مبلغ:</strong>
                                        <div id="summary-amount" class="text-primary"></div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>کمیسیون:</strong>
                                        <div id="summary-commission" class="text-success"></div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>مدت زمان:</strong>
                                        <div id="summary-duration" class="text-info"></div>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>تاریخ انقضا:</strong>
                                        <div id="summary-expiry" class="text-warning"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ route('admin.purchases.index') }}" class="btn btn-outline-secondary">
                                انصراف
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check"></i> فعال‌سازی پلن
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const planSelect = document.getElementById('plan_id');
    const agentSelect = document.getElementById('agent_id');
    const amountInput = document.getElementById('amount');
    const durationInput = document.getElementById('duration_days');
    const summaryCard = document.getElementById('summary-card');

    function updateSummary() {
        const selectedPlan = planSelect.options[planSelect.selectedIndex];
        const selectedAgent = agentSelect.options[agentSelect.selectedIndex];
        
        if (!selectedPlan.value) {
            summaryCard.style.display = 'none';
            return;
        }

        const planPrice = parseFloat(selectedPlan.dataset.price) || 0;
        const planDuration = parseInt(selectedPlan.dataset.duration) || 0;
        const agentCommission = parseFloat(selectedAgent.dataset.commission) || 0;
        
        const finalAmount = parseFloat(amountInput.value) || planPrice;
        const finalDuration = parseInt(durationInput.value) || planDuration;
        const commissionAmount = (finalAmount * agentCommission) / 100;
        
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + finalDuration);
        
        document.getElementById('summary-amount').textContent = finalAmount.toLocaleString() + ' تومان';
        document.getElementById('summary-commission').textContent = commissionAmount.toLocaleString() + ' تومان';
        document.getElementById('summary-duration').textContent = finalDuration + ' روز';
        document.getElementById('summary-expiry').textContent = expiryDate.toLocaleDateString('fa-IR');
        
        summaryCard.style.display = 'block';
    }

    planSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            if (!amountInput.value) {
                amountInput.placeholder = selectedOption.dataset.price + ' تومان';
            }
            if (!durationInput.value) {
                durationInput.placeholder = selectedOption.dataset.duration + ' روز';
            }
        }
        updateSummary();
    });

    agentSelect.addEventListener('change', updateSummary);
    amountInput.addEventListener('input', updateSummary);
    durationInput.addEventListener('input', updateSummary);
});
</script>
@endsection
