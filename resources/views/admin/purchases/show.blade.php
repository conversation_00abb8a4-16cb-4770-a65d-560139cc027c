@extends('layouts.admin')

@section('title', 'جزئیات خرید #' . $purchase->id)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">جزئیات خرید #{{ $purchase->id }}</h1>
                <div>
                    <a href="{{ route('admin.purchases.edit', $purchase) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> ویرایش
                    </a>
                    <a href="{{ route('admin.purchases.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> بازگشت
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Purchase Details -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات خرید</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>شناسه خرید:</strong></td>
                                            <td>#{{ $purchase->id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>شناسه تراکنش:</strong></td>
                                            <td>{{ $purchase->transaction_id ?: 'ندارد' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>مبلغ:</strong></td>
                                            <td><strong class="text-primary">{{ number_format($purchase->amount) }} تومان</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>روش پرداخت:</strong></td>
                                            <td>{{ $purchase->payment_method ?: 'نامشخص' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>وضعیت:</strong></td>
                                            <td>
                                                @php
                                                    $statusClasses = [
                                                        'pending' => 'warning',
                                                        'completed' => 'success',
                                                        'failed' => 'danger',
                                                        'refunded' => 'secondary'
                                                    ];
                                                    $statusTexts = [
                                                        'pending' => 'در انتظار',
                                                        'completed' => 'تکمیل شده',
                                                        'failed' => 'ناموفق',
                                                        'refunded' => 'بازگشت داده شده'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusClasses[$purchase->status] ?? 'secondary' }}">
                                                    {{ $statusTexts[$purchase->status] ?? $purchase->status }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>تاریخ ایجاد:</strong></td>
                                            <td>{{ $purchase->created_at->format('Y/m/d H:i') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاریخ فعال‌سازی:</strong></td>
                                            <td>{{ $purchase->activated_at ? $purchase->activated_at->format('Y/m/d H:i') : 'فعال نشده' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>تاریخ انقضا:</strong></td>
                                            <td>
                                                @if($purchase->expires_at)
                                                    {{ $purchase->expires_at->format('Y/m/d H:i') }}
                                                    @if($purchase->isActive())
                                                        <br><small class="text-success">{{ $purchase->remaining_days }} روز باقی‌مانده</small>
                                                    @elseif($purchase->isExpired())
                                                        <br><small class="text-danger">منقضی شده</small>
                                                    @endif
                                                @else
                                                    نامحدود
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>وضعیت فعلی:</strong></td>
                                            <td>
                                                @if($purchase->isActive())
                                                    <span class="badge bg-success">فعال</span>
                                                @elseif($purchase->isExpired() && $purchase->isCompleted())
                                                    <span class="badge bg-danger">منقضی شده</span>
                                                @elseif($purchase->isCompleted())
                                                    <span class="badge bg-info">تکمیل شده</span>
                                                @else
                                                    <span class="badge bg-warning">غیرفعال</span>
                                                @endif
                                            </td>
                                        </tr>
                                        @if($purchase->notes)
                                        <tr>
                                            <td><strong>یادداشت:</strong></td>
                                            <td>{{ $purchase->notes }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- User Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات کاربر</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>نام:</strong></td>
                                            <td>{{ $purchase->user->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>ایمیل:</strong></td>
                                            <td>{{ $purchase->user->email }}</td>
                                        </tr>
                                        @if($purchase->user->phone)
                                        <tr>
                                            <td><strong>تلفن:</strong></td>
                                            <td>{{ $purchase->user->phone }}</td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>تاریخ عضویت:</strong></td>
                                            <td>{{ $purchase->user->created_at->format('Y/m/d') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>کل خریدها:</strong></td>
                                            <td>{{ $purchase->user->purchases()->count() }} خرید</td>
                                        </tr>
                                        <tr>
                                            <td><strong>کل مبلغ خریدها:</strong></td>
                                            <td>{{ number_format($purchase->user->purchases()->where('status', 'completed')->sum('amount')) }} تومان</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Plan Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات پلن</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>نام پلن:</strong></td>
                                            <td>{{ $purchase->plan->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>نوع:</strong></td>
                                            <td>{{ $purchase->plan->type ?: 'نامشخص' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>قیمت اصلی:</strong></td>
                                            <td>{{ number_format($purchase->plan->price) }} تومان</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>مدت زمان:</strong></td>
                                            <td>{{ $purchase->plan->formatted_duration }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>وضعیت پلن:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $purchase->plan->status === 'active' ? 'success' : 'warning' }}">
                                                    {{ $purchase->plan->status === 'active' ? 'فعال' : 'غیرفعال' }}
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            @if($purchase->plan->description)
                            <div class="mt-3">
                                <strong>توضیحات:</strong>
                                <p class="mt-2">{{ $purchase->plan->description }}</p>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Agent Information -->
                    @if($purchase->agent)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">اطلاعات نماینده</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>نام نماینده:</strong></td>
                                            <td>{{ $purchase->agent->user->name }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>کد معرف:</strong></td>
                                            <td><code>{{ $purchase->agent->referral_code }}</code></td>
                                        </tr>
                                        <tr>
                                            <td><strong>نرخ کمیسیون:</strong></td>
                                            <td>{{ $purchase->agent->commission_rate }}%</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>مبلغ کمیسیون:</strong></td>
                                            <td><strong class="text-success">{{ number_format($purchase->commission_amount) }} تومان</strong></td>
                                        </tr>
                                        @if($purchase->commission)
                                        <tr>
                                            <td><strong>وضعیت کمیسیون:</strong></td>
                                            <td>
                                                @php
                                                    $commissionStatusClasses = [
                                                        'pending' => 'warning',
                                                        'paid' => 'success',
                                                        'cancelled' => 'danger'
                                                    ];
                                                    $commissionStatusTexts = [
                                                        'pending' => 'در انتظار',
                                                        'paid' => 'پرداخت شده',
                                                        'cancelled' => 'لغو شده'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $commissionStatusClasses[$purchase->commission->status] ?? 'secondary' }}">
                                                    {{ $commissionStatusTexts[$purchase->commission->status] ?? $purchase->commission->status }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endif
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>

                <!-- Actions Sidebar -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">عملیات</h5>
                        </div>
                        <div class="card-body">
                            <!-- Status Toggle -->
                            <form method="POST" action="{{ route('admin.purchases.toggle-status', $purchase) }}" class="mb-3">
                                @csrf
                                <button type="submit" class="btn btn-{{ $purchase->status === 'completed' ? 'warning' : 'success' }} w-100">
                                    <i class="fas fa-power-off"></i>
                                    {{ $purchase->status === 'completed' ? 'غیرفعال کردن' : 'فعال کردن' }}
                                </button>
                            </form>

                            <!-- Extend Subscription -->
                            @if($purchase->isCompleted())
                            <form method="POST" action="{{ route('admin.purchases.extend', $purchase) }}" class="mb-3">
                                @csrf
                                <div class="input-group mb-2">
                                    <input type="number" class="form-control" name="extend_days" 
                                           placeholder="تعداد روز" min="1" max="365" required>
                                    <span class="input-group-text">روز</span>
                                </div>
                                <button type="submit" class="btn btn-info w-100">
                                    <i class="fas fa-calendar-plus"></i> تمدید اشتراک
                                </button>
                            </form>
                            @endif

                            <!-- Edit Purchase -->
                            <a href="{{ route('admin.purchases.edit', $purchase) }}" class="btn btn-primary w-100 mb-3">
                                <i class="fas fa-edit"></i> ویرایش خرید
                            </a>

                            <!-- Delete Purchase -->
                            <form method="POST" action="{{ route('admin.purchases.destroy', $purchase) }}" 
                                  onsubmit="return confirm('آیا از حذف این خرید اطمینان دارید؟')" class="mb-3">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="fas fa-trash"></i> حذف خرید
                                </button>
                            </form>

                            <!-- Quick Actions -->
                            <hr>
                            <h6>دسترسی سریع</h6>
                            <a href="{{ route('admin.purchases.create', ['user_id' => $purchase->user_id]) }}" class="btn btn-outline-success btn-sm w-100 mb-2">
                                <i class="fas fa-plus"></i> فعال‌سازی پلن جدید برای این کاربر
                            </a>
                            <a href="{{ route('admin.purchases.index', ['search' => $purchase->user->email]) }}" class="btn btn-outline-info btn-sm w-100">
                                <i class="fas fa-search"></i> مشاهده سایر خریدهای این کاربر
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
