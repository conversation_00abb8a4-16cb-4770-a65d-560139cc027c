@extends('layouts.admin')

@section('title', 'مدیریت خریدها')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">مدیریت خریدها</h1>
                <a href="{{ route('admin.purchases.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> فعال‌سازی پلن جدید
                </a>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">{{ number_format($stats['total_purchases']) }}</h5>
                            <p class="card-text small">کل خریدها</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">{{ number_format($stats['completed_purchases']) }}</h5>
                            <p class="card-text small">خریدهای تکمیل شده</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">{{ number_format($stats['pending_purchases']) }}</h5>
                            <p class="card-text small">خریدهای در انتظار</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">{{ number_format($stats['total_revenue']) }} تومان</h5>
                            <p class="card-text small">کل درآمد</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">{{ number_format($stats['active_subscriptions']) }}</h5>
                            <p class="card-text small">اشتراک‌های فعال</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.purchases.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">جستجو</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="نام کاربر، ایمیل، پلن یا شناسه تراکنش">
                            </div>
                            <div class="col-md-2">
                                <label for="status" class="form-label">وضعیت</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">همه</option>
                                    <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>در انتظار</option>
                                    <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>تکمیل شده</option>
                                    <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>ناموفق</option>
                                    <option value="refunded" {{ request('status') === 'refunded' ? 'selected' : '' }}>بازگشت داده شده</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="plan_id" class="form-label">پلن</label>
                                <select class="form-select" id="plan_id" name="plan_id">
                                    <option value="">همه پلن‌ها</option>
                                    @foreach($plans as $plan)
                                        <option value="{{ $plan->id }}" {{ request('plan_id') == $plan->id ? 'selected' : '' }}>
                                            {{ $plan->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="agent_id" class="form-label">نماینده</label>
                                <select class="form-select" id="agent_id" name="agent_id">
                                    <option value="">همه نمایندگان</option>
                                    @foreach($agents as $agent)
                                        <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                            {{ $agent->user->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label for="date_from" class="form-label">از تاریخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request('date_from') }}">
                            </div>
                            <div class="col-md-1">
                                <label for="date_to" class="form-label">تا تاریخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request('date_to') }}">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i>
                                </button>
                                <a href="{{ route('admin.purchases.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Purchases Table -->
            <div class="card">
                <div class="card-body">
                    @if($purchases->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>شناسه</th>
                                        <th>کاربر</th>
                                        <th>پلن</th>
                                        <th>مبلغ</th>
                                        <th>وضعیت</th>
                                        <th>نماینده</th>
                                        <th>تاریخ انقضا</th>
                                        <th>تاریخ ایجاد</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($purchases as $purchase)
                                        <tr>
                                            <td>
                                                <small class="text-muted">#{{ $purchase->id }}</small><br>
                                                @if($purchase->transaction_id)
                                                    <small class="text-muted">{{ $purchase->transaction_id }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $purchase->user->name }}</strong><br>
                                                <small class="text-muted">{{ $purchase->user->email }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ $purchase->plan->name }}</strong><br>
                                                <small class="text-muted">{{ $purchase->plan->type }}</small>
                                            </td>
                                            <td>
                                                <strong>{{ number_format($purchase->amount) }} تومان</strong>
                                                @if($purchase->commission_amount > 0)
                                                    <br><small class="text-success">کمیسیون: {{ number_format($purchase->commission_amount) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                @php
                                                    $statusClasses = [
                                                        'pending' => 'warning',
                                                        'completed' => 'success',
                                                        'failed' => 'danger',
                                                        'refunded' => 'secondary'
                                                    ];
                                                    $statusTexts = [
                                                        'pending' => 'در انتظار',
                                                        'completed' => 'تکمیل شده',
                                                        'failed' => 'ناموفق',
                                                        'refunded' => 'بازگشت داده شده'
                                                    ];
                                                @endphp
                                                <span class="badge bg-{{ $statusClasses[$purchase->status] ?? 'secondary' }}">
                                                    {{ $statusTexts[$purchase->status] ?? $purchase->status }}
                                                </span>
                                                @if($purchase->isActive())
                                                    <br><small class="text-success">فعال ({{ $purchase->remaining_days }} روز باقی‌مانده)</small>
                                                @elseif($purchase->isExpired() && $purchase->isCompleted())
                                                    <br><small class="text-danger">منقضی شده</small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($purchase->agent)
                                                    <strong>{{ $purchase->agent->user->name }}</strong><br>
                                                    <small class="text-muted">{{ $purchase->agent->referral_code }}</small>
                                                @else
                                                    <span class="text-muted">بدون نماینده</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($purchase->expires_at)
                                                    {{ $purchase->expires_at->format('Y/m/d') }}<br>
                                                    <small class="text-muted">{{ $purchase->expires_at->format('H:i') }}</small>
                                                @else
                                                    <span class="text-muted">نامحدود</span>
                                                @endif
                                            </td>
                                            <td>
                                                {{ $purchase->created_at->format('Y/m/d') }}<br>
                                                <small class="text-muted">{{ $purchase->created_at->format('H:i') }}</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.purchases.show', $purchase) }}" class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.purchases.edit', $purchase) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $purchases->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">هیچ خریدی یافت نشد</h5>
                            <p class="text-muted">برای فعال‌سازی پلن جدید از دکمه بالا استفاده کنید.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
