<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>پلن‌های ما</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Tahoma', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .plan-card { 
            transition: transform 0.3s, box-shadow 0.3s; 
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .plan-card:hover { 
            transform: translateY(-10px); 
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        .plan-banner {
            height: 200px;
            object-fit: cover;
            width: 100%;
        }
        .plan-type {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1;
        }
        .price-tag {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1.5em;
            font-weight: bold;
            margin: 15px 0;
            display: inline-block;
        }
        .feature-list {
            text-align: right;
        }
        .feature-list li {
            padding: 5px 0;
            border-bottom: 1px solid #f8f9fa;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .btn-purchase {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: bold;
            transition: all 0.3s;
        }
        .btn-purchase:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.4);
        }
        .hero-section {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            text-align: center;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <!-- Hero Section -->
        <div class="hero-section">
            <h1 class="display-4 mb-3">🚀 پلن‌های ویژه ما</h1>
            <p class="lead">بهترین پلن را برای نیازهای خود انتخاب کنید</p>
            @if(request('ref'))
                <div class="alert alert-info">
                    <strong>🎉 شما از طریق کد معرف {{ request('ref') }} وارد شده‌اید!</strong>
                    <br>از تخفیف‌های ویژه بهره‌مند شوید.
                </div>
            @endif
        </div>

        <!-- Plans Grid -->
        <div class="row">
            @forelse($plans as $plan)
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card plan-card h-100">
                    @if($plan->type)
                        <span class="badge bg-primary plan-type">{{ $plan->type }}</span>
                    @endif
                    
                    @if($plan->banner)
                        <img src="{{ $plan->banner_url }}" class="plan-banner" alt="بنر {{ $plan->name }}">
                    @else
                        <div class="plan-banner bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
                            <h3 class="text-white">{{ $plan->name }}</h3>
                        </div>
                    @endif
                    
                    <div class="card-body text-center">
                        <h4 class="card-title">{{ $plan->name }}</h4>
                        
                        <div class="price-tag">
                            {{ $plan->formatted_price }}
                        </div>
                        
                        <p class="text-muted">{{ $plan->formatted_duration }}</p>
                        
                        @if($plan->description)
                            <p class="card-text">{{ $plan->description }}</p>
                        @endif
                        
                        @if($plan->features)
                            <ul class="list-unstyled feature-list">
                                @foreach($plan->features as $feature)
                                    <li><i class="text-success">✓</i> {{ $feature }}</li>
                                @endforeach
                            </ul>
                        @endif
                    </div>
                    
                    <div class="card-footer bg-transparent text-center">
                        @if($plan->status === 'active')
                            <form method="POST" action="{{ route('purchase.create') }}">
                                @csrf
                                <input type="hidden" name="plan_id" value="{{ $plan->id }}">
                                @if(request('ref'))
                                    <input type="hidden" name="referral_code" value="{{ request('ref') }}">
                                @endif
                                <button type="submit" class="btn btn-primary btn-purchase">
                                    🛒 خرید این پلن
                                </button>
                            </form>
                        @else
                            <button class="btn btn-secondary" disabled>
                                غیرفعال
                            </button>
                        @endif
                        
                        @if($plan->link)
                            <div class="mt-2">
                                <a href="{{ $plan->link }}" target="_blank" class="btn btn-outline-info btn-sm">
                                    📖 اطلاعات بیشتر
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            @empty
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h4>هیچ پلنی در دسترس نیست</h4>
                    <p>لطفاً بعداً مراجعه کنید.</p>
                </div>
            </div>
            @endforelse
        </div>
        
        <!-- Footer -->
        <div class="text-center mt-5 mb-3">
            <div class="hero-section">
                <h5 class="text-white">🤝 نماینده هستید؟</h5>
                <p class="text-white-50">با ما همکاری کنید و کمیسیون دریافت کنید</p>
                <a href="{{ route('login') }}" class="btn btn-outline-light">
                    ورود به پنل نماینده
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
