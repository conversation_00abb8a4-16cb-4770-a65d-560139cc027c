<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'پنل مدیریت')</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Tahoma', sans-serif; 
            background-color: #f8f9fa;
        }
        .stats-card { 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .navbar-nav .nav-link {
            margin: 0 5px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .navbar-nav .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
        }
        .navbar-nav .nav-link.active {
            background-color: rgba(255,255,255,0.2);
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #e9ecef;
            border-radius: 10px 10px 0 0 !important;
        }
        .btn {
            border-radius: 8px;
        }
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        .badge {
            font-size: 0.75em;
        }
    </style>
    @stack('styles')
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ route('admin.dashboard') }}">
                <i class="fas fa-cogs"></i> پنل مدیریت
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <div class="navbar-nav me-auto">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" 
                       href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i> داشبورد
                    </a>
                    <a class="nav-link {{ request()->routeIs('admin.agents.*') ? 'active' : '' }}" 
                       href="{{ route('admin.agents.index') }}">
                        <i class="fas fa-users"></i> نمایندگان
                    </a>
                    <a class="nav-link {{ request()->routeIs('admin.plans.*') ? 'active' : '' }}"
                       href="{{ route('admin.plans.index') }}">
                        <i class="fas fa-box"></i> پلن‌ها
                    </a>
                    <a class="nav-link {{ request()->routeIs('admin.purchases.*') ? 'active' : '' }}"
                       href="{{ route('admin.purchases.index') }}">
                        <i class="fas fa-shopping-cart"></i> خریدها
                    </a>
                    <a class="nav-link {{ request()->routeIs('admin.commissions.*') ? 'active' : '' }}"
                       href="{{ route('admin.commissions.index') }}">
                        <i class="fas fa-percentage"></i> کمیسیون‌ها
                    </a>
                    <a class="nav-link {{ request()->routeIs('admin.withdrawals.*') ? 'active' : '' }}" 
                       href="{{ route('admin.withdrawals.index') }}">
                        <i class="fas fa-money-bill-wave"></i> درخواست‌های تسویه
                    </a>
                    <a class="nav-link {{ request()->routeIs('admin.telegram-bots.*') ? 'active' : '' }}" 
                       href="{{ route('admin.telegram-bots.index') }}">
                        <i class="fab fa-telegram"></i> ربات‌های تلگرام
                    </a>
                </div>
                
                <div class="navbar-nav">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ auth()->user()->name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                    @csrf
                                    <button type="submit" class="dropdown-item">
                                        <i class="fas fa-sign-out-alt"></i> خروج
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="container-fluid mt-4">
        @yield('content')
    </main>

    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                © {{ date('Y') }} سیستم مدیریت نمایندگان. تمامی حقوق محفوظ است.
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    @stack('scripts')
</body>
</html>
