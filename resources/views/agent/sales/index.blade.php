<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فروش‌های من</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link active" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link" href="{{ route('agent.wallet-settings') }}">تنظیمات کیف پول</a>
            </div>
            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-outline-light btn-sm">خروج</button>
            </form>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4">فروش‌های من</h1>

        <!-- آمار فروش -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>کل فروش</h5>
                        <h2>{{ number_format($stats['total_sales']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>تعداد فروش</h5>
                        <h2>{{ number_format($stats['total_count']) }}</h2>
                        <small>خرید</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>فروش این ماه</h5>
                        <h2>{{ number_format($stats['this_month_sales']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>در انتظار پرداخت</h5>
                        <h2>{{ number_format($stats['pending_sales']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <input type="text" name="search" class="form-control" placeholder="جستجو در نام مشتری یا پلن" value="{{ request('search') }}">
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>تکمیل شده</option>
                            <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>ناموفق</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}" placeholder="از تاریخ">
                    </div>
                    <div class="col-md-2">
                        <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}" placeholder="تا تاریخ">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                        <a href="{{ route('agent.sales.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست فروش‌ها -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>شناسه</th>
                                <th>مشتری</th>
                                <th>پلن</th>
                                <th>مبلغ</th>
                                <th>کمیسیون</th>
                                <th>وضعیت</th>
                                <th>تاریخ خرید</th>
                                <th>تاریخ انقضا</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($sales as $sale)
                            <tr>
                                <td>#{{ $sale->id }}</td>
                                <td>
                                    <div>
                                        <strong>{{ $sale->user->name }}</strong><br>
                                        <small class="text-muted">{{ $sale->user->email }}</small>
                                    </div>
                                </td>
                                <td>{{ $sale->plan->name }}</td>
                                <td>{{ number_format($sale->amount) }} تومان</td>
                                <td>{{ number_format($sale->commission_amount) }} تومان</td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $sale->status === 'completed' ? 'success' : 
                                        ($sale->status === 'failed' ? 'danger' : 'warning') 
                                    }}">
                                        {{ 
                                            $sale->status === 'completed' ? 'تکمیل شده' : 
                                            ($sale->status === 'failed' ? 'ناموفق' : 'در انتظار') 
                                        }}
                                    </span>
                                    @if($sale->isActive())
                                        <br><small class="text-success">فعال</small>
                                    @elseif($sale->isExpired() && $sale->isCompleted())
                                        <br><small class="text-danger">منقضی شده</small>
                                    @endif
                                </td>
                                <td>{{ $sale->created_at->format('Y/m/d H:i') }}</td>
                                <td>
                                    @if($sale->expires_at)
                                        {{ $sale->expires_at->format('Y/m/d') }}
                                        @if($sale->isActive())
                                            <br><small class="text-info">{{ $sale->remaining_days }} روز باقی</small>
                                        @endif
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>
                                    <a href="{{ route('agent.sales.show', $sale) }}" class="btn btn-sm btn-outline-primary">
                                        جزئیات
                                    </a>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">هیچ فروشی یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $sales->links() }}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
