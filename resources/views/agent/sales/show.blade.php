<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جزئیات فروش #{{ $purchase->id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .status-timeline {
            position: relative;
            padding-left: 30px;
        }
        .status-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -23px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
        }
        .timeline-item.active::before {
            background: #28a745;
        }
        .timeline-item.current::before {
            background: #ffc107;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link active" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link" href="{{ route('agent.wallet-settings') }}">تنظیمات کیف پول</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>جزئیات فروش #{{ $purchase->id }}</h1>
            <a href="{{ route('agent.sales.index') }}" class="btn btn-secondary">بازگشت</a>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- اطلاعات خرید -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات خرید</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>شناسه خرید:</strong></td>
                                        <td>#{{ $purchase->id }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مبلغ:</strong></td>
                                        <td class="text-success"><strong>{{ number_format($purchase->amount) }} تومان</strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>کمیسیون شما:</strong></td>
                                        <td class="text-primary"><strong>{{ number_format($purchase->commission_amount) }} تومان</strong></td>
                                    </tr>
                                    <tr>
                                        <td><strong>وضعیت:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $purchase->status === 'completed' ? 'success' : 
                                                ($purchase->status === 'failed' ? 'danger' : 'warning') 
                                            }}">
                                                {{ 
                                                    $purchase->status === 'completed' ? 'تکمیل شده' : 
                                                    ($purchase->status === 'failed' ? 'ناموفق' : 'در انتظار') 
                                                }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>تاریخ خرید:</strong></td>
                                        <td>{{ $purchase->created_at->format('Y/m/d H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاریخ فعال‌سازی:</strong></td>
                                        <td>{{ $purchase->activated_at ? $purchase->activated_at->format('Y/m/d H:i') : '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تاریخ انقضا:</strong></td>
                                        <td>{{ $purchase->expires_at ? $purchase->expires_at->format('Y/m/d H:i') : '-' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>روش پرداخت:</strong></td>
                                        <td>{{ $purchase->payment_method }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if($purchase->isActive())
                            <div class="alert alert-success">
                                <strong>✅ اشتراک فعال است</strong><br>
                                {{ $purchase->remaining_days }} روز تا انقضا باقی مانده
                            </div>
                        @elseif($purchase->isExpired() && $purchase->isCompleted())
                            <div class="alert alert-warning">
                                <strong>⏰ اشتراک منقضی شده</strong><br>
                                این اشتراک در تاریخ {{ $purchase->expires_at->format('Y/m/d') }} منقضی شده است
                            </div>
                        @endif
                    </div>
                </div>

                <!-- اطلاعات مشتری -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>اطلاعات مشتری</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>نام:</strong></td>
                                        <td>{{ $purchase->user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>ایمیل:</strong></td>
                                        <td>{{ $purchase->user->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>تلفن:</strong></td>
                                        <td>{{ $purchase->user->phone ?: 'وارد نشده' }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>تاریخ عضویت:</strong></td>
                                        <td>{{ $purchase->user->created_at->format('Y/m/d') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>کل خریدها از شما:</strong></td>
                                        <td>{{ $purchase->user->purchases()->where('agent_id', auth()->user()->agent->id)->count() }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>کل مبلغ خریدها:</strong></td>
                                        <td>{{ number_format($purchase->user->purchases()->where('agent_id', auth()->user()->agent->id)->sum('amount')) }} تومان</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اطلاعات پلن -->
                <div class="card">
                    <div class="card-header">
                        <h5>اطلاعات پلن</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>{{ $purchase->plan->name }}</h4>
                                <p class="text-muted">{{ $purchase->plan->description }}</p>
                                
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>قیمت:</strong></td>
                                        <td>{{ $purchase->plan->formatted_price }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مدت زمان:</strong></td>
                                        <td>{{ $purchase->plan->formatted_duration }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                @if($purchase->plan->features)
                                    <strong>ویژگی‌های پلن:</strong>
                                    <ul class="mt-2">
                                        @foreach($purchase->plan->features as $feature)
                                            <li>{{ $feature }}</li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- وضعیت خرید -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>مراحل خرید</h5>
                    </div>
                    <div class="card-body">
                        <div class="status-timeline">
                            <div class="timeline-item active">
                                <strong>خرید ثبت شد</strong>
                                <br><small class="text-muted">{{ $purchase->created_at->format('Y/m/d H:i') }}</small>
                            </div>
                            
                            <div class="timeline-item {{ $purchase->status === 'completed' ? 'active' : ($purchase->status === 'pending' ? 'current' : '') }}">
                                <strong>در انتظار پرداخت</strong>
                                @if($purchase->status === 'pending')
                                    <br><small class="text-warning">در انتظار تکمیل پرداخت</small>
                                @endif
                            </div>
                            
                            @if($purchase->status !== 'failed')
                            <div class="timeline-item {{ $purchase->status === 'completed' ? 'active' : '' }}">
                                <strong>پرداخت تکمیل شد</strong>
                                @if($purchase->status === 'completed')
                                    <br><small class="text-success">{{ $purchase->activated_at ? $purchase->activated_at->format('Y/m/d H:i') : $purchase->updated_at->format('Y/m/d H:i') }}</small>
                                @endif
                            </div>
                            
                            <div class="timeline-item {{ $purchase->status === 'completed' ? 'active' : '' }}">
                                <strong>اشتراک فعال شد</strong>
                                @if($purchase->status === 'completed')
                                    <br><small class="text-success">اشتراک تا {{ $purchase->expires_at->format('Y/m/d') }} فعال است</small>
                                @endif
                            </div>
                            @else
                            <div class="timeline-item active">
                                <strong>پرداخت ناموفق</strong>
                                <br><small class="text-danger">خرید ناموفق بود</small>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- اطلاعات کمیسیون -->
                @if($purchase->commission)
                <div class="card">
                    <div class="card-header">
                        <h5>اطلاعات کمیسیون</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>مبلغ کمیسیون:</strong></td>
                                <td class="text-success"><strong>{{ number_format($purchase->commission->amount) }} تومان</strong></td>
                            </tr>
                            <tr>
                                <td><strong>وضعیت:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $purchase->commission->status === 'paid' ? 'success' : 
                                        ($purchase->commission->status === 'cancelled' ? 'danger' : 'warning') 
                                    }}">
                                        {{ 
                                            $purchase->commission->status === 'paid' ? 'پرداخت شده' : 
                                            ($purchase->commission->status === 'cancelled' ? 'لغو شده' : 'در انتظار') 
                                        }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاریخ ایجاد:</strong></td>
                                <td>{{ $purchase->commission->created_at->format('Y/m/d H:i') }}</td>
                            </tr>
                            @if($purchase->commission->paid_at)
                            <tr>
                                <td><strong>تاریخ پرداخت:</strong></td>
                                <td>{{ $purchase->commission->paid_at->format('Y/m/d H:i') }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
