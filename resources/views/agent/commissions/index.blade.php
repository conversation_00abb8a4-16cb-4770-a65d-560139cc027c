<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>کمیسیون‌های من</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link active" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link" href="{{ route('agent.wallet-settings') }}">تنظیمات کیف پول</a>
            </div>
            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                @csrf
                <button type="submit" class="btn btn-outline-light btn-sm">خروج</button>
            </form>
        </div>
    </nav>

    <div class="container mt-4">
        <h1 class="mb-4">کمیسیون‌های من</h1>

        <!-- آمار کمیسیون -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>کل کمیسیون</h5>
                        <h2>{{ number_format($stats['total_commission']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>در انتظار پرداخت</h5>
                        <h2>{{ number_format($stats['pending_commission']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>پرداخت شده</h5>
                        <h2>{{ number_format($stats['paid_commission']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>موجودی کیف پول</h5>
                        <h2>{{ number_format($stats['wallet_balance']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- کمیسیون این ماه -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">💰 کمیسیون این ماه</h5>
                    </div>
                    <div class="card-body text-center">
                        <h3 class="text-primary">{{ number_format($stats['this_month_commission']) }} تومان</h3>
                        <p class="text-muted">از {{ now()->format('Y/m/01') }} تا {{ now()->format('Y/m/d') }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">🎯 دسترسی سریع</h5>
                    </div>
                    <div class="card-body text-center">
                        <a href="{{ route('agent.withdrawals.create') }}" class="btn btn-success mb-2">
                            درخواست تسویه حساب
                        </a><br>
                        <a href="{{ route('agent.sales.index') }}" class="btn btn-outline-primary">
                            مشاهده فروش‌ها
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار</option>
                            <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>پرداخت شده</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>لغو شده</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}" placeholder="از تاریخ">
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}" placeholder="تا تاریخ">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                        <a href="{{ route('agent.commissions.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست کمیسیون‌ها -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>شناسه</th>
                                <th>خرید</th>
                                <th>مشتری</th>
                                <th>پلن</th>
                                <th>مبلغ فروش</th>
                                <th>مبلغ کمیسیون</th>
                                <th>وضعیت</th>
                                <th>تاریخ ایجاد</th>
                                <th>تاریخ پرداخت</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($commissions as $commission)
                            <tr>
                                <td>#{{ $commission->id }}</td>
                                <td>#{{ $commission->purchase_id }}</td>
                                <td>
                                    <div>
                                        <strong>{{ $commission->purchase->user->name }}</strong><br>
                                        <small class="text-muted">{{ $commission->purchase->user->email }}</small>
                                    </div>
                                </td>
                                <td>{{ $commission->purchase->plan->name }}</td>
                                <td>{{ number_format($commission->purchase->amount) }} تومان</td>
                                <td>
                                    <strong class="text-success">{{ number_format($commission->amount) }} تومان</strong>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $commission->status === 'paid' ? 'success' : 
                                        ($commission->status === 'cancelled' ? 'danger' : 'warning') 
                                    }}">
                                        {{ 
                                            $commission->status === 'paid' ? 'پرداخت شده' : 
                                            ($commission->status === 'cancelled' ? 'لغو شده' : 'در انتظار') 
                                        }}
                                    </span>
                                </td>
                                <td>{{ $commission->created_at->format('Y/m/d H:i') }}</td>
                                <td>
                                    {{ $commission->paid_at ? $commission->paid_at->format('Y/m/d H:i') : '-' }}
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="9" class="text-center">هیچ کمیسیونی یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $commissions->links() }}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
