@extends('layouts.agent')

@section('title', 'مدیریت کاربران تلگرام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">👥 مدیریت کاربران تلگرام</h1>
                <a href="{{ route('agent.telegram.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> بازگشت به مدیریت ربات
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <!-- آمار کاربران -->
        <div class="col-md-3 mb-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['total_users'] }}</h4>
                            <p class="card-text">کل کاربران</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['active_users'] }}</h4>
                            <p class="card-text">کاربران فعال</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['registered_users'] }}</h4>
                            <p class="card-text">ثبت‌نام شده</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $telegramBot->is_active ? 'فعال' : 'غیرفعال' }}</h4>
                            <p class="card-text">وضعیت ربات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-robot fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">🔍 جستجو و فیلتر</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('agent.telegram.users') }}">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="search" class="form-label">جستجو</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="نام، نام کاربری یا شناسه...">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="registered" class="form-label">وضعیت ثبت‌نام</label>
                                <select class="form-select" id="registered" name="registered">
                                    <option value="">همه</option>
                                    <option value="1" {{ request('registered') === '1' ? 'selected' : '' }}>ثبت‌نام شده</option>
                                    <option value="0" {{ request('registered') === '0' ? 'selected' : '' }}>ثبت‌نام نشده</option>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="active" class="form-label">وضعیت فعالیت</label>
                                <select class="form-select" id="active" name="active">
                                    <option value="">همه</option>
                                    <option value="1" {{ request('active') === '1' ? 'selected' : '' }}>فعال (30 روز اخیر)</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> جستجو
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📋 لیست کاربران تلگرام</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>کاربر</th>
                                    <th>شناسه تلگرام</th>
                                    <th>وضعیت</th>
                                    <th>آخرین فعالیت</th>
                                    <th>تاریخ عضویت</th>
                                    <th>عملیات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($users as $user)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ $user->getFullName() }}</strong>
                                            @if($user->telegram_username)
                                                <br><small class="text-muted">@{{ $user->telegram_username }}</small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>{{ $user->telegram_user_id }}</td>
                                    <td>
                                        @if($user->is_registered)
                                            <span class="badge bg-success">ثبت‌نام شده</span>
                                        @else
                                            <span class="badge bg-secondary">ثبت‌نام نشده</span>
                                        @endif
                                        
                                        @if($user->isActive())
                                            <span class="badge bg-info">فعال</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($user->last_interaction)
                                            {{ $user->last_interaction->format('Y/m/d H:i') }}
                                            <br>
                                            <small class="text-muted">{{ $user->last_interaction->diffForHumans() }}</small>
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        {{ $user->created_at->format('Y/m/d H:i') }}
                                        <br>
                                        <small class="text-muted">{{ $user->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="showSendMessageModal({{ $user->id }}, '{{ $user->getFullName() }}')">
                                            <i class="fas fa-paper-plane"></i> ارسال پیام
                                        </button>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                                            <p>هیچ کاربری یافت نشد.</p>
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $users->withQueryString()->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for sending message -->
<div class="modal fade" id="sendMessageModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ارسال پیام به <span id="recipientName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="sendMessageForm">
                    <input type="hidden" id="user_id" name="user_id">
                    <div class="mb-3">
                        <label for="message" class="form-label">پیام</label>
                        <textarea class="form-control" id="message" name="message" rows="4" 
                                  placeholder="پیام خود را وارد کنید..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-primary" onclick="sendMessage()">ارسال پیام</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function showSendMessageModal(userId, userName) {
    document.getElementById('user_id').value = userId;
    document.getElementById('recipientName').textContent = userName;
    new bootstrap.Modal(document.getElementById('sendMessageModal')).show();
}

function sendMessage() {
    const form = document.getElementById('sendMessageForm');
    const formData = new FormData(form);

    fetch('{{ route("agent.telegram.send-message") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            bootstrap.Modal.getInstance(document.getElementById('sendMessageModal')).hide();
            form.reset();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در ارسال پیام');
    });
}
</script>
@endpush
