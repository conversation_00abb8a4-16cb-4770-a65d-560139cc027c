@extends('layouts.agent')

@section('title', 'مدیریت ربات تلگرام')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">🤖 مدیریت ربات تلگرام</h1>
                @if($telegramBot && $telegramBot->is_active)
                    <span class="badge bg-success fs-6">ربات فعال</span>
                @elseif($telegramBot)
                    <span class="badge bg-warning fs-6">ربات غیرفعال</span>
                @else
                    <span class="badge bg-secondary fs-6">ربات تنظیم نشده</span>
                @endif
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <!-- آمار ربات -->
        <div class="col-md-3 mb-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['total_users'] }}</h4>
                            <p class="card-text">کل کاربران</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['active_users'] }}</h4>
                            <p class="card-text">کاربران فعال</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['registered_users'] }}</h4>
                            <p class="card-text">ثبت‌نام شده</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ $stats['bot_configured'] ? 'تنظیم شده' : 'تنظیم نشده' }}</h4>
                            <p class="card-text">وضعیت ربات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-robot fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- تنظیمات ربات -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">⚙️ تنظیمات ربات تلگرام</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('agent.telegram.store') }}">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="bot_token" class="form-label">توکن ربات <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="bot_token" name="bot_token" 
                                   value="{{ old('bot_token', $telegramBot->bot_token ?? '') }}" 
                                   placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz" required>
                            <div class="form-text">
                                توکن ربات را از @BotFather در تلگرام دریافت کنید.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bot_username" class="form-label">نام کاربری ربات <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">@</span>
                                <input type="text" class="form-control" id="bot_username" name="bot_username" 
                                       value="{{ old('bot_username', $telegramBot->bot_username ?? '') }}" 
                                       placeholder="my_agent_bot" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="welcome_message" class="form-label">پیام خوشامدگویی</label>
                            <textarea class="form-control" id="welcome_message" name="welcome_message" rows="5"
                                      placeholder="پیام خوشامدگویی سفارشی خود را وارد کنید...">{{ old('welcome_message', $telegramBot->welcome_message ?? '') }}</textarea>
                            <div class="form-text">
                                در صورت خالی بودن، پیام پیش‌فرض نمایش داده می‌شود.
                            </div>
                        </div>

                        <!-- Mandatory Channel Settings -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="card-title mb-0">📢 تنظیمات کانال اجباری</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="mandatory_channel_username" class="form-label">نام کاربری کانال</label>
                                    <div class="input-group">
                                        <span class="input-group-text">@</span>
                                        <input type="text" class="form-control" id="mandatory_channel_username" name="mandatory_channel_username"
                                               value="{{ old('mandatory_channel_username', $telegramBot->mandatory_channel_username ?? '') }}"
                                               placeholder="my_channel">
                                    </div>
                                    <div class="form-text">
                                        نام کاربری کانال (بدون @). اگر پر شود، کاربران باید قبل از استفاده از ربات عضو این کانال شوند.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="mandatory_channel_id" class="form-label">شناسه کانال</label>
                                    <input type="text" class="form-control" id="mandatory_channel_id" name="mandatory_channel_id"
                                           value="{{ old('mandatory_channel_id', $telegramBot->mandatory_channel_id ?? '') }}"
                                           placeholder="-1001234567890">
                                    <div class="form-text">
                                        شناسه عددی کانال (مثل -1001234567890). در صورت وجود نام کاربری، این فیلد اختیاری است.
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="mandatory_channel_title" class="form-label">عنوان کانال</label>
                                    <input type="text" class="form-control" id="mandatory_channel_title" name="mandatory_channel_title"
                                           value="{{ old('mandatory_channel_title', $telegramBot->mandatory_channel_title ?? '') }}"
                                           placeholder="کانال رسمی ما">
                                    <div class="form-text">
                                        عنوان کانال که در پیام‌های ربات نمایش داده می‌شود.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> ذخیره تنظیمات
                            </button>
                            
                            @if($telegramBot)
                                <button type="button" class="btn btn-outline-info" onclick="testBot()">
                                    <i class="fas fa-vial"></i> تست ربات
                                </button>

                                @if(app()->environment('local'))
                                    <button type="button" class="btn btn-outline-success" onclick="simulateWebhook()">
                                        <i class="fas fa-play"></i> شبیه‌سازی
                                    </button>
                                @endif

                                <button type="button" class="btn btn-outline-{{ $telegramBot->is_active ? 'warning' : 'success' }}"
                                        onclick="toggleBotStatus()">
                                    <i class="fas fa-power-off"></i>
                                    {{ $telegramBot->is_active ? 'غیرفعال کردن' : 'فعال کردن' }}
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- اطلاعات ربات -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📊 اطلاعات ربات</h5>
                </div>
                <div class="card-body">
                    @if($telegramBot)
                        <div class="mb-3">
                            <strong>نام کاربری:</strong><br>
                            <code>@{{ $telegramBot->bot_username ?? 'نامشخص' }}</code>
                        </div>
                        
                        <div class="mb-3">
                            <strong>وضعیت:</strong><br>
                            <span class="badge bg-{{ $telegramBot->is_active ? 'success' : 'warning' }}">
                                {{ $telegramBot->is_active ? 'فعال' : 'غیرفعال' }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <strong>Webhook:</strong><br>
                            <span class="badge bg-{{ $telegramBot->hasWebhook() ? 'success' : 'danger' }}">
                                {{ $telegramBot->hasWebhook() ? 'تنظیم شده' : 'تنظیم نشده' }}
                            </span>
                        </div>
                        
                        @if($telegramBot->webhook_set_at)
                            <div class="mb-3">
                                <strong>آخرین تنظیم Webhook:</strong><br>
                                <small>{{ $telegramBot->webhook_set_at->format('Y/m/d H:i') }}</small>
                            </div>
                        @endif
                        
                        <div class="mb-3">
                            <strong>لینک ربات:</strong><br>
                            @if($telegramBot->bot_username)
                                <a href="https://t.me/{{ $telegramBot->bot_username }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fab fa-telegram"></i> باز کردن در تلگرام
                                </a>
                            @else
                                <span class="text-muted">نام کاربری تنظیم نشده</span>
                            @endif
                        </div>
                    @else
                        <p class="text-muted">ربات تلگرام تنظیم نشده است.</p>
                        <p class="small">
                            برای شروع، ابتدا یک ربات از @BotFather در تلگرام ایجاد کنید و توکن آن را در فرم کنار وارد کنید.
                        </p>
                    @endif
                </div>
            </div>

            @if($telegramBot && $stats['total_users'] > 0)
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">🚀 عملیات سریع</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('agent.telegram.users') }}" class="btn btn-outline-primary">
                                <i class="fas fa-users"></i> مدیریت کاربران
                            </a>
                            <button type="button" class="btn btn-outline-success" onclick="showBroadcastModal()">
                                <i class="fas fa-bullhorn"></i> ارسال پیام همگانی
                            </button>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

<!-- Modal for broadcast message -->
<div class="modal fade" id="broadcastModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ارسال پیام همگانی</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="broadcastForm">
                    <div class="mb-3">
                        <label for="broadcast_target" class="form-label">مخاطبان</label>
                        <select class="form-select" id="broadcast_target" name="target" required>
                            <option value="all">همه کاربران</option>
                            <option value="active">کاربران فعال (30 روز اخیر)</option>
                            <option value="registered">کاربران ثبت‌نام شده</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="broadcast_message" class="form-label">پیام</label>
                        <textarea class="form-control" id="broadcast_message" name="message" rows="4" 
                                  placeholder="پیام خود را وارد کنید..." required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">انصراف</button>
                <button type="button" class="btn btn-primary" onclick="sendBroadcast()">ارسال پیام</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script>
function testBot() {
    fetch('{{ route("agent.telegram.test") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تست ربات');
    });
}

function toggleBotStatus() {
    if (!confirm('آیا از تغییر وضعیت ربات اطمینان دارید؟')) {
        return;
    }

    fetch('{{ route("agent.telegram.toggle-status") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در تغییر وضعیت ربات');
    });
}

function showBroadcastModal() {
    new bootstrap.Modal(document.getElementById('broadcastModal')).show();
}

function simulateWebhook() {
    @if($telegramBot)
        fetch('{{ url("api/telegram/webhook/{$telegramBot->id}/simulate") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ شبیه‌سازی webhook با موفقیت انجام شد!\n\nپیام /start از کاربر تست ارسال شد.');
            } else {
                alert('❌ ' + data.message);
            }
        })
        .catch(error => {
            alert('❌ خطا در شبیه‌سازی webhook');
        });
    @endif
}

function sendBroadcast() {
    const form = document.getElementById('broadcastForm');
    const formData = new FormData(form);

    if (!confirm('آیا از ارسال پیام همگانی اطمینان دارید؟')) {
        return;
    }

    fetch('{{ route("agent.telegram.broadcast") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
            bootstrap.Modal.getInstance(document.getElementById('broadcastModal')).hide();
            form.reset();
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطا در ارسال پیام');
    });
}
</script>
@endpush
