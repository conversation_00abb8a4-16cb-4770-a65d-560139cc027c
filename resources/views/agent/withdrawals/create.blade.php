<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>درخواست تسویه حساب جدید</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .method-card { cursor: pointer; transition: all 0.3s; }
        .method-card:hover { transform: translateY(-2px); box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .method-card.selected { border-color: #0d6efd; background-color: #f8f9ff; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link active" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link" href="{{ route('agent.wallet-settings') }}">تنظیمات کیف پول</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>درخواست تسویه حساب جدید</h1>
            <a href="{{ route('agent.withdrawals.index') }}" class="btn btn-secondary">
                بازگشت
            </a>
        </div>

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- اطلاعات کیف پول -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5 class="text-primary">موجودی فعلی</h5>
                        <h3>{{ number_format($agent->wallet_balance) }} تومان</h3>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-info">حداقل برداشت</h5>
                        <h3>{{ number_format($agent->getMinWithdrawalAmount()) }} تومان</h3>
                    </div>
                    <div class="col-md-4">
                        <h5 class="text-success">قابل برداشت</h5>
                        <h3>{{ number_format(max(0, $agent->wallet_balance - $agent->withdrawalRequests()->pending()->sum('amount'))) }} تومان</h3>
                    </div>
                </div>
            </div>
        </div>

        <form method="POST" action="{{ route('agent.withdrawals.store') }}">
            @csrf
            
            <!-- مبلغ درخواست -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>مبلغ درخواست</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="amount" class="form-label">مبلغ (تومان)</label>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   min="{{ $agent->getMinWithdrawalAmount() }}" 
                                   max="{{ $agent->wallet_balance }}"
                                   value="{{ old('amount') }}" required>
                            <div class="form-text">
                                حداقل: {{ number_format($agent->getMinWithdrawalAmount()) }} تومان
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="agent_notes" class="form-label">توضیحات (اختیاری)</label>
                            <textarea class="form-control" id="agent_notes" name="agent_notes" rows="3">{{ old('agent_notes') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- انتخاب روش پرداخت -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>روش پرداخت</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- کیف پول کریپتو -->
                        <div class="col-md-6 mb-3">
                            <div class="card method-card" onclick="selectMethod('crypto')">
                                <div class="card-body text-center">
                                    <h5>💰 کیف پول کریپتو</h5>
                                    <p class="text-muted">پرداخت سریع و امن با ارزهای دیجیتال</p>
                                    <input type="radio" name="method" value="crypto" id="method_crypto" 
                                           {{ old('method') == 'crypto' ? 'checked' : '' }}>
                                </div>
                            </div>
                        </div>

                        <!-- حساب بانکی -->
                        <div class="col-md-6 mb-3">
                            <div class="card method-card" onclick="selectMethod('bank')">
                                <div class="card-body text-center">
                                    <h5>🏦 حساب بانکی</h5>
                                    <p class="text-muted">انتقال به حساب بانکی</p>
                                    <input type="radio" name="method" value="bank" id="method_bank"
                                           {{ old('method') == 'bank' ? 'checked' : '' }}>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اطلاعات کیف پول کریپتو -->
            <div class="card mb-4" id="crypto_details" style="display: none;">
                <div class="card-header">
                    <h5>اطلاعات کیف پول کریپتو</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="crypto_wallet_type" class="form-label">نوع ارز</label>
                            <select class="form-select" id="crypto_wallet_type" name="crypto_wallet_type">
                                <option value="">انتخاب کنید</option>
                                <option value="BTC" {{ old('crypto_wallet_type') == 'BTC' ? 'selected' : '' }}>Bitcoin (BTC)</option>
                                <option value="ETH" {{ old('crypto_wallet_type') == 'ETH' ? 'selected' : '' }}>Ethereum (ETH)</option>
                                <option value="USDT" {{ old('crypto_wallet_type') == 'USDT' ? 'selected' : '' }}>Tether (USDT)</option>
                                <option value="USDC" {{ old('crypto_wallet_type') == 'USDC' ? 'selected' : '' }}>USD Coin (USDC)</option>
                                <option value="BNB" {{ old('crypto_wallet_type') == 'BNB' ? 'selected' : '' }}>Binance Coin (BNB)</option>
                                <option value="TRX" {{ old('crypto_wallet_type') == 'TRX' ? 'selected' : '' }}>TRON (TRX)</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="crypto_network" class="form-label">شبکه</label>
                            <select class="form-select" id="crypto_network" name="crypto_network">
                                <option value="">انتخاب کنید</option>
                                <option value="BTC" {{ old('crypto_network') == 'BTC' ? 'selected' : '' }}>Bitcoin Network</option>
                                <option value="ETH" {{ old('crypto_network') == 'ETH' ? 'selected' : '' }}>Ethereum (ERC20)</option>
                                <option value="TRC20" {{ old('crypto_network') == 'TRC20' ? 'selected' : '' }}>TRON (TRC20)</option>
                                <option value="BEP20" {{ old('crypto_network') == 'BEP20' ? 'selected' : '' }}>Binance Smart Chain (BEP20)</option>
                                <option value="POLYGON" {{ old('crypto_network') == 'POLYGON' ? 'selected' : '' }}>Polygon Network</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="crypto_wallet_address" class="form-label">آدرس کیف پول</label>
                            <input type="text" class="form-control" id="crypto_wallet_address" name="crypto_wallet_address" 
                                   value="{{ old('crypto_wallet_address', $agent->crypto_wallet_address) }}" 
                                   placeholder="آدرس کیف پول خود را وارد کنید">
                        </div>
                    </div>
                </div>
            </div>

            <!-- اطلاعات حساب بانکی -->
            <div class="card mb-4" id="bank_details" style="display: none;">
                <div class="card-header">
                    <h5>اطلاعات حساب بانکی</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="bank_name" class="form-label">نام بانک</label>
                            <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                   value="{{ old('bank_name', $agent->bank_name) }}" 
                                   placeholder="مثال: بانک ملی">
                        </div>
                        <div class="col-md-4">
                            <label for="bank_account_name" class="form-label">نام صاحب حساب</label>
                            <input type="text" class="form-control" id="bank_account_name" name="bank_account_name" 
                                   value="{{ old('bank_account_name', $agent->bank_account_name) }}" 
                                   placeholder="نام کامل صاحب حساب">
                        </div>
                        <div class="col-md-4">
                            <label for="bank_account_number" class="form-label">شماره حساب</label>
                            <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" 
                                   value="{{ old('bank_account_number', $agent->bank_account_number) }}" 
                                   placeholder="شماره حساب بانکی">
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">ثبت درخواست</button>
                <a href="{{ route('agent.withdrawals.index') }}" class="btn btn-secondary btn-lg">انصراف</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function selectMethod(method) {
            // Remove selected class from all cards
            document.querySelectorAll('.method-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');
            
            // Check the radio button
            document.getElementById('method_' + method).checked = true;
            
            // Show/hide details
            document.getElementById('crypto_details').style.display = method === 'crypto' ? 'block' : 'none';
            document.getElementById('bank_details').style.display = method === 'bank' ? 'block' : 'none';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            const checkedMethod = document.querySelector('input[name="method"]:checked');
            if (checkedMethod) {
                selectMethod(checkedMethod.value);
            }
        });
    </script>
</body>
</html>
