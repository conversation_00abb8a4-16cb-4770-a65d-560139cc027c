<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>درخواست‌های تسویه حساب</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
        .stats-card { border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link active" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link" href="{{ route('agent.wallet-settings') }}">تنظیمات کیف پول</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>درخواست‌های تسویه حساب</h1>
            <a href="{{ route('agent.withdrawals.create') }}" class="btn btn-primary">
                درخواست جدید
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- آمار کیف پول -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>موجودی کیف پول</h5>
                        <h2>{{ number_format($stats['wallet_balance']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>کل برداشت‌ها</h5>
                        <h2>{{ number_format($stats['total_withdrawn']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>در انتظار پردازش</h5>
                        <h2>{{ number_format($stats['pending_amount']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>حداقل برداشت</h5>
                        <h2>{{ number_format($stats['min_withdrawal']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- فیلترها -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">همه وضعیت‌ها</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>در انتظار</option>
                            <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>تایید شده</option>
                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>در حال پردازش</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>تکمیل شده</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>رد شده</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="method" class="form-select">
                            <option value="">همه روش‌ها</option>
                            <option value="crypto" {{ request('method') == 'crypto' ? 'selected' : '' }}>کیف پول کریپتو</option>
                            <option value="bank" {{ request('method') == 'bank' ? 'selected' : '' }}>حساب بانکی</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary">فیلتر</button>
                        <a href="{{ route('agent.withdrawals.index') }}" class="btn btn-outline-secondary">پاک کردن</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- لیست درخواست‌ها -->
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>شناسه</th>
                                <th>مبلغ</th>
                                <th>روش پرداخت</th>
                                <th>وضعیت</th>
                                <th>تاریخ درخواست</th>
                                <th>تاریخ پردازش</th>
                                <th>عملیات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($withdrawals as $withdrawal)
                            <tr>
                                <td>#{{ $withdrawal->id }}</td>
                                <td>{{ $withdrawal->formatted_amount }}</td>
                                <td>
                                    <span class="badge bg-{{ $withdrawal->method === 'crypto' ? 'warning' : 'info' }}">
                                        {{ $withdrawal->method_in_persian }}
                                    </span>
                                    @if($withdrawal->isCrypto())
                                        <br><small class="text-muted">{{ $withdrawal->crypto_type_name }}</small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $withdrawal->status === 'completed' ? 'success' : 
                                        ($withdrawal->status === 'rejected' ? 'danger' : 
                                        ($withdrawal->status === 'processing' ? 'info' : 'warning')) 
                                    }}">
                                        {{ $withdrawal->status_in_persian }}
                                    </span>
                                </td>
                                <td>{{ $withdrawal->created_at->format('Y/m/d H:i') }}</td>
                                <td>{{ $withdrawal->processed_at ? $withdrawal->processed_at->format('Y/m/d H:i') : '-' }}</td>
                                <td>
                                    <a href="{{ route('agent.withdrawals.show', $withdrawal) }}" class="btn btn-sm btn-outline-primary">
                                        مشاهده
                                    </a>
                                    @if($withdrawal->isPending())
                                        <form method="POST" action="{{ route('agent.withdrawals.cancel', $withdrawal) }}" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    onclick="return confirm('آیا از لغو این درخواست اطمینان دارید؟')">
                                                لغو
                                            </button>
                                        </form>
                                    @endif
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center">هیچ درخواستی یافت نشد</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                {{ $withdrawals->links() }}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
