@extends('layouts.agent')

@section('title', 'داشبورد نماینده')

@section('content')
    <div class="container">
        <h1 class="mb-4">داشبورد نماینده</h1>
        
        <!-- کد معرف -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>کد معرف شما</h5>
                    </div>
                    <div class="card-body">
                        <div class="referral-code">
                            {{ $stats['referral_code'] }}
                        </div>
                        <p class="mt-2 text-muted text-center">
                            این کد را با مشتریان خود به اشتراک بگذارید
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- آمار کلی -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-primary text-white">
                    <div class="card-body">
                        <h5>کل فروش</h5>
                        <h2>{{ number_format($stats['total_sales']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-success text-white">
                    <div class="card-body">
                        <h5>کل کمیسیون</h5>
                        <h2>{{ number_format($stats['total_commission']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-info text-white">
                    <div class="card-body">
                        <h5>موجودی کیف پول</h5>
                        <h2>{{ number_format($stats['wallet_balance']) }}</h2>
                        <small>تومان</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card bg-warning text-white">
                    <div class="card-body">
                        <h5>کل مشتریان</h5>
                        <h2>{{ number_format($stats['total_customers']) }}</h2>
                        <small>نفر</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- آمار ماهانه و تسویه حساب -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>فروش این ماه</h5>
                    </div>
                    <div class="card-body">
                        <h3 class="text-primary">{{ number_format($stats['this_month_sales']) }} تومان</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>کمیسیون این ماه</h5>
                    </div>
                    <div class="card-body">
                        <h3 class="text-success">{{ number_format($stats['this_month_commission']) }} تومان</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5>💰 تسویه حساب سریع</h5>
                    </div>
                    <div class="card-body text-center">
                        <p class="mb-2">موجودی قابل برداشت:</p>
                        <h4 class="text-success mb-3">{{ number_format($stats['wallet_balance']) }} تومان</h4>
                        <a href="{{ route('agent.withdrawals.create') }}" class="btn btn-primary btn-sm">
                            درخواست تسویه
                        </a>
                        <a href="{{ route('agent.wallet-settings') }}" class="btn btn-outline-secondary btn-sm">
                            تنظیمات کیف پول
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- آخرین فروش‌ها -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>آخرین فروش‌ها</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>مشتری</th>
                                        <th>پلن</th>
                                        <th>مبلغ</th>
                                        <th>کمیسیون</th>
                                        <th>وضعیت</th>
                                        <th>تاریخ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recent_sales as $sale)
                                    <tr>
                                        <td>{{ $sale->user->name }}</td>
                                        <td>{{ $sale->plan->name }}</td>
                                        <td>{{ number_format($sale->amount) }} تومان</td>
                                        <td>{{ number_format($sale->commission_amount) }} تومان</td>
                                        <td>
                                            <span class="badge bg-{{ $sale->status === 'completed' ? 'success' : 'warning' }}">
                                                {{ $sale->status === 'completed' ? 'تکمیل شده' : 'در انتظار' }}
                                            </span>
                                        </td>
                                        <td>{{ $sale->created_at->format('Y/m/d') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>آخرین کمیسیون‌ها</h5>
                    </div>
                    <div class="card-body">
                        @foreach($recent_commissions as $commission)
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <strong>{{ $commission->purchase->plan->name }}</strong><br>
                                <small class="text-muted">{{ $commission->created_at->format('Y/m/d') }}</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ number_format($commission->amount) }}</strong><br>
                                <small class="badge bg-{{ $commission->status === 'paid' ? 'success' : 'warning' }}">
                                    {{ $commission->status === 'paid' ? 'پرداخت شده' : 'در انتظار' }}
                                </small>
                            </div>
                        </div>
                        <hr>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
