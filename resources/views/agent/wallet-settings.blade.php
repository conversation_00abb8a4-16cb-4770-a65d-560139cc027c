<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظیمات کیف پول</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Tahoma', sans-serif; }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-success">
        <div class="container">
            <a class="navbar-brand" href="#">پنل نماینده</a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="{{ route('agent.dashboard') }}">داشبورد</a>
                <a class="nav-link" href="{{ route('agent.sales.index') }}">فروش‌ها</a>
                <a class="nav-link" href="{{ route('agent.commissions.index') }}">کمیسیون‌ها</a>
                <a class="nav-link" href="{{ route('agent.withdrawals.index') }}">تسویه حساب</a>
                <a class="nav-link active" href="{{ route('agent.wallet-settings') }}">تنظیمات کیف پول</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>تنظیمات کیف پول</h1>
            <a href="{{ route('agent.dashboard') }}" class="btn btn-secondary">
                بازگشت به داشبورد
            </a>
        </div>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <form method="POST" action="{{ route('agent.wallet-settings.update') }}">
            @csrf
            
            <!-- اطلاعات کیف پول کریپتو -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>💰 کیف پول کریپتو</h5>
                    <small class="text-muted">برای دریافت پرداخت‌های سریع کریپتو</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="crypto_wallet_type" class="form-label">نوع ارز</label>
                            <select class="form-select" id="crypto_wallet_type" name="crypto_wallet_type">
                                <option value="">انتخاب کنید</option>
                                <option value="BTC" {{ $agent->crypto_wallet_type == 'BTC' ? 'selected' : '' }}>Bitcoin (BTC)</option>
                                <option value="ETH" {{ $agent->crypto_wallet_type == 'ETH' ? 'selected' : '' }}>Ethereum (ETH)</option>
                                <option value="USDT" {{ $agent->crypto_wallet_type == 'USDT' ? 'selected' : '' }}>Tether (USDT)</option>
                                <option value="USDC" {{ $agent->crypto_wallet_type == 'USDC' ? 'selected' : '' }}>USD Coin (USDC)</option>
                                <option value="BNB" {{ $agent->crypto_wallet_type == 'BNB' ? 'selected' : '' }}>Binance Coin (BNB)</option>
                                <option value="TRX" {{ $agent->crypto_wallet_type == 'TRX' ? 'selected' : '' }}>TRON (TRX)</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="crypto_network" class="form-label">شبکه</label>
                            <select class="form-select" id="crypto_network" name="crypto_network">
                                <option value="">انتخاب کنید</option>
                                <option value="BTC" {{ $agent->crypto_network == 'BTC' ? 'selected' : '' }}>Bitcoin Network</option>
                                <option value="ETH" {{ $agent->crypto_network == 'ETH' ? 'selected' : '' }}>Ethereum (ERC20)</option>
                                <option value="TRC20" {{ $agent->crypto_network == 'TRC20' ? 'selected' : '' }}>TRON (TRC20)</option>
                                <option value="BEP20" {{ $agent->crypto_network == 'BEP20' ? 'selected' : '' }}>Binance Smart Chain (BEP20)</option>
                                <option value="POLYGON" {{ $agent->crypto_network == 'POLYGON' ? 'selected' : '' }}>Polygon Network</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="crypto_wallet_address" class="form-label">آدرس کیف پول</label>
                            <input type="text" class="form-control" id="crypto_wallet_address" name="crypto_wallet_address" 
                                   value="{{ $agent->crypto_wallet_address }}" 
                                   placeholder="آدرس کیف پول خود را وارد کنید">
                        </div>
                    </div>
                    
                    @if($agent->hasCryptoWallet())
                        <div class="alert alert-success">
                            <strong>✅ کیف پول کریپتو تنظیم شده</strong><br>
                            نوع: {{ \App\Models\WithdrawalRequest::CRYPTO_TYPES[$agent->crypto_wallet_type] ?? $agent->crypto_wallet_type }}<br>
                            شبکه: {{ \App\Models\WithdrawalRequest::CRYPTO_NETWORKS[$agent->crypto_network] ?? $agent->crypto_network }}<br>
                            آدرس: {{ substr($agent->crypto_wallet_address, 0, 10) }}...{{ substr($agent->crypto_wallet_address, -10) }}
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <strong>⚠️ کیف پول کریپتو تنظیم نشده</strong><br>
                            برای استفاده از پرداخت کریپتو، لطفاً اطلاعات کیف پول خود را تکمیل کنید.
                        </div>
                    @endif
                </div>
            </div>

            <!-- اطلاعات حساب بانکی -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>🏦 حساب بانکی</h5>
                    <small class="text-muted">برای دریافت پرداخت‌های بانکی</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="bank_name" class="form-label">نام بانک</label>
                            <input type="text" class="form-control" id="bank_name" name="bank_name" 
                                   value="{{ $agent->bank_name }}" 
                                   placeholder="مثال: بانک ملی">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="bank_account_name" class="form-label">نام صاحب حساب</label>
                            <input type="text" class="form-control" id="bank_account_name" name="bank_account_name" 
                                   value="{{ $agent->bank_account_name }}" 
                                   placeholder="نام کامل صاحب حساب">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="bank_account_number" class="form-label">شماره حساب</label>
                            <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" 
                                   value="{{ $agent->bank_account_number }}" 
                                   placeholder="شماره حساب بانکی">
                        </div>
                    </div>
                    
                    @if($agent->hasBankAccount())
                        <div class="alert alert-success">
                            <strong>✅ حساب بانکی تنظیم شده</strong><br>
                            بانک: {{ $agent->bank_name }}<br>
                            صاحب حساب: {{ $agent->bank_account_name }}<br>
                            شماره حساب: {{ $agent->bank_account_number }}
                        </div>
                    @else
                        <div class="alert alert-warning">
                            <strong>⚠️ حساب بانکی تنظیم نشده</strong><br>
                            برای استفاده از پرداخت بانکی، لطفاً اطلاعات حساب بانکی خود را تکمیل کنید.
                        </div>
                    @endif
                </div>
            </div>

            <!-- راهنمای امنیت -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>🔒 نکات امنیتی</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">🔹 همیشه آدرس کیف پول خود را دوبار بررسی کنید</li>
                        <li class="mb-2">🔹 از کیف پول‌های معتبر و امن استفاده کنید</li>
                        <li class="mb-2">🔹 آدرس کیف پول را از منابع قابل اعتماد کپی کنید</li>
                        <li class="mb-2">🔹 اطلاعات حساب بانکی خود را با دقت وارد کنید</li>
                        <li class="mb-2">🔹 در صورت تغییر اطلاعات، حتماً آن‌ها را به‌روزرسانی کنید</li>
                    </ul>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg">ذخیره تنظیمات</button>
                <a href="{{ route('agent.dashboard') }}" class="btn btn-secondary btn-lg">انصراف</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
