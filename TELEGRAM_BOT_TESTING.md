# راهنمای تست سیستم ربات تلگرام

## 🚀 راه‌اندازی اولیه

### 1. اجرای مهاجرت‌ها
```bash
php artisan migrate
```

### 2. ایجاد ربات در تلگرام
1. به @BotFather در تلگرام پیام دهید
2. دستور `/newbot` را ارسال کنید
3. نام نمایشی ربات را وارد کنید (مثلاً "ربات نماینده علی")
4. نام کاربری ربات را وارد کنید (باید به `bot` ختم شود، مثلاً `ali_agent_bot`)
5. توکن دریافتی را کپی کنید

### 3. تنظیم ربات در پنل ادمین
1. وارد پنل ادمین شوید
2. به بخش "ربات‌های تلگرام" بروید
3. روی "افزودن ربات جدید" کلیک کنید
4. نماینده مورد نظر را انتخاب کنید
5. توکن و نام کاربری ربات را وارد کنید
6. پیام خوشامدگویی دلخواه را وارد کنید (اختیاری)
7. روی "ذخیره ربات" کلیک کنید

## 🧪 تست در محیط Local

### محدودیت‌های محیط Local
- تلگرام نمی‌تواند به localhost دسترسی داشته باشد
- Webhook به صورت خودکار skip می‌شود
- برای تست از شبیه‌سازی استفاده می‌کنیم

### روش‌های تست

#### 1. تست اتصال ربات
- در لیست ربات‌ها روی دکمه "تست" (آیکون شیشه آزمایش) کلیک کنید
- این تست اتصال به API تلگرام را بررسی می‌کند

#### 2. شبیه‌سازی Webhook (فقط در محیط Local)
- در لیست ربات‌ها روی دکمه سبز "شبیه‌سازی" (آیکون play) کلیک کنید
- این عمل یک پیام `/start` از کاربر تست شبیه‌سازی می‌کند
- نتیجه در لاگ‌ها قابل مشاهده است

#### 3. بررسی لاگ‌ها
```bash
tail -f storage/logs/laravel.log
```

## 🔧 عملکرد دستورات ربات

### دستورات پشتیبانی شده:
- `/start` - پیام خوشامدگویی + آمار ربات
- `/help` - راهنمای استفاده
- `/plans` - نمایش پلن‌ها با دکمه ورود به miniapp
- `/register` - هدایت به ثبت‌نام با دکمه miniapp
- `/status` - نمایش وضعیت کاربر

### نحوه کارکرد:
1. کاربر دستور ارسال می‌کند
2. اطلاعات کاربر در جدول `telegram_users` ذخیره می‌شود
3. ربات پاسخ مناسب ارسال می‌کند
4. آخرین تعامل کاربر به‌روزرسانی می‌شود

## 📊 مدیریت کاربران

### پنل نماینده:
- مشاهده آمار کاربران تلگرام
- لیست کاربران با فیلتر و جستجو
- ارسال پیام به کاربران
- ارسال پیام همگانی

### پنل ادمین:
- مدیریت تمام ربات‌ها
- مشاهده آمار هر ربات
- فعال/غیرفعال کردن ربات‌ها
- تنظیم مجدد webhook

## 🌐 تنظیمات Production

### متغیرهای محیط مورد نیاز:
```env
APP_ENV=production
APP_URL=https://yourdomain.com
```

### تنظیم Webhook در Production:
- Webhook به صورت خودکار تنظیم می‌شود
- URL webhook: `https://yourdomain.com/api/telegram/webhook/{bot_id}`
- تلگرام به این URL درخواست‌ها را ارسال می‌کند

## 🐛 عیب‌یابی

### مشکلات رایج:

#### 1. خطای "Bot not found"
- بررسی کنید توکن ربات صحیح باشد
- مطمئن شوید ربات در BotFather ایجاد شده

#### 2. خطای "Webhook failed"
- در محیط local این طبیعی است
- در production بررسی کنید URL در دسترس باشد

#### 3. ربات پاسخ نمی‌دهد
- وضعیت ربات را بررسی کنید (فعال/غیرفعال)
- لاگ‌ها را بررسی کنید
- تست اتصال انجام دهید

### بررسی لاگ‌ها:
```bash
# مشاهده لاگ‌های عمومی
tail -f storage/logs/laravel.log

# فیلتر لاگ‌های تلگرام
tail -f storage/logs/laravel.log | grep -i telegram
```

## 📝 نکات مهم

### امنیت:
- توکن ربات را محرمانه نگه دارید
- از HTTPS در production استفاده کنید
- دسترسی به endpoint های webhook را محدود کنید

### عملکرد:
- کاربران غیرفعال (بیش از 30 روز) جداگانه شمارش می‌شوند
- آمار به صورت real-time محاسبه می‌شود
- پیام‌های همگانی با تاخیر ارسال می‌شوند (rate limiting)

### محدودیت‌ها:
- هر نماینده فقط یک ربات تلگرام دارد
- پیام‌ها حداکثر 4000 کاراکتر
- ارسال همگانی با تاخیر 0.1 ثانیه بین پیام‌ها

## 🔗 لینک‌های مفید

- [مستندات Telegram Bot API](https://core.telegram.org/bots/api)
- [راهنمای BotFather](https://core.telegram.org/bots#6-botfather)
- [Webhook راهنما](https://core.telegram.org/bots/api#setwebhook)

## 📞 پشتیبانی

در صورت بروز مشکل:
1. ابتدا لاگ‌ها را بررسی کنید
2. تست اتصال ربات انجام دهید
3. در محیط local از شبیه‌سازی استفاده کنید
4. وضعیت webhook را بررسی کنید
