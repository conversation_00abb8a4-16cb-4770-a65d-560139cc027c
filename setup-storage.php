<?php

/**
 * Storage Setup Script
 * Run this script to create storage symlink and directories
 */

echo "🔧 Setting up storage...\n";

// Create storage symlink
if (!file_exists('public/storage')) {
    if (symlink('../storage/app/public', 'public/storage')) {
        echo "✅ Storage symlink created successfully\n";
    } else {
        echo "❌ Failed to create storage symlink\n";
    }
} else {
    echo "✅ Storage symlink already exists\n";
}

// Create plan-banners directory
$planBannersDir = 'storage/app/public/plan-banners';
if (!is_dir($planBannersDir)) {
    if (mkdir($planBannersDir, 0755, true)) {
        echo "✅ Plan banners directory created\n";
    } else {
        echo "❌ Failed to create plan banners directory\n";
    }
} else {
    echo "✅ Plan banners directory already exists\n";
}

echo "🎉 Storage setup completed!\n";
echo "📁 You can now upload plan banners to: {$planBannersDir}\n";
echo "🌐 Files will be accessible via: public/storage/plan-banners/\n";
