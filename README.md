# سیستم فروش پلن با کمیسیون نمایندگان و تسویه حساب کریپتو

سیستم جامع فروش پلن‌های اشتراکی با قابلیت مدیریت نمایندگان، محاسبه کمیسیون خودکار و تسویه حساب با کیف پول کریپتو

## ویژگی‌های کلیدی

### 🔐 سیستم احراز هویت چندنقشه
- **ادمین**: مدیریت کامل سیستم، تایید درخواست‌های تسویه
- **نماینده**: مشاهده آمار فروش، کمیسیون‌ها و درخواست تسویه حساب
- **مشتری**: خرید پلن‌ها از طریق API با کد معرف

### 👥 مدیریت نمایندگان
- ایجاد و مدیریت نمایندگان
- تولید کد معرف منحصربه‌فرد
- تنظیم درصد کمیسیون برای هر نماینده
- مدیریت اطلاعات بانکی و کیف پول کریپتو

### 💰 سیستم کمیسیون و تسویه حساب پیشرفته
- محاسبه خودکار کمیسیون پس از خرید موفق
- کیف پول مجازی برای نمایندگان
- **درخواست تسویه حساب با کیف پول کریپتو**
- **پشتیبانی از ارزهای مختلف**: Bitcoin, Ethereum, USDT, USDC, BNB, TRON
- **پشتیبانی از شبکه‌های مختلف**: Bitcoin Network, ERC20, TRC20, BEP20, Polygon
- مدیریت درخواست‌های تسویه توسط ادمین
- پیگیری وضعیت درخواست‌ها (در انتظار، تایید شده، در حال پردازش، تکمیل شده)

### 📦 مدیریت پلن‌ها
- ایجاد پلن‌های مختلف با قیمت و مدت زمان متفاوت
- تعریف ویژگی‌ها برای هر پلن
- فعال/غیرفعال کردن پلن‌ها

### 🛒 سیستم خرید
- خرید پلن‌ها با کد معرف
- پیگیری وضعیت خریدها
- مدیریت تاریخ انقضا

## نصب و راه‌اندازی

### پیش‌نیازها
- PHP 8.2+
- Composer
- MySQL/MariaDB
- Laravel 12

### مراحل نصب

```bash
# کلون پروژه
git clone <repository-url>
cd PlanAgentes

# نصب وابستگی‌ها
composer install

# تنظیم محیط
cp .env.example .env
php artisan key:generate

# تنظیم دیتابیس در .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=planagentes
DB_USERNAME=root
DB_PASSWORD=

# اجرای مایگریشن‌ها و داده‌های اولیه
php artisan migrate
php artisan db:seed

# راه‌اندازی سرور
php artisan serve
```

## حساب‌های کاربری پیش‌فرض

پس از اجرای seeder:

- **ادمین**: `<EMAIL>` / `password`
- **نماینده**: `<EMAIL>` / `password` (کد معرف تولید خودکار)
- **مشتری**: `<EMAIL>` / `password`

## ویژگی‌های تسویه حساب کریپتو

### برای نمایندگان:
- **تنظیم کیف پول کریپتو**: امکان اضافه کردن آدرس کیف پول برای ارزهای مختلف
- **انتخاب شبکه**: پشتیبانی از شبکه‌های مختلف برای هر ارز
- **درخواست تسویه**: ثبت درخواست برداشت با حداقل مبلغ تعریف شده
- **پیگیری وضعیت**: مشاهده وضعیت درخواست‌های تسویه
- **تنظیمات امنیتی**: راهنمای امنیت برای کیف پول‌های کریپتو

### برای ادمین:
- **مدیریت درخواست‌ها**: مشاهده، تایید، رد یا تکمیل درخواست‌های تسویه
- **عملیات دسته‌ای**: تایید چندین درخواست به صورت همزمان
- **پیگیری تراکنش**: ثبت هش تراکنش برای پرداخت‌های کریپتو
- **گزارش‌گیری**: خروجی Excel از درخواست‌های تسویه
- **آمار کامل**: نمایش آمار درخواست‌های مختلف

## API Documentation

### Authentication
```http
POST /api/register
POST /api/login
GET /api/profile
```

### Plans
```http
GET /api/plans
GET /api/plans/{id}
```

### Purchases
```http
POST /api/purchases
GET /api/purchases
GET /api/purchases/{id}
```

### نمونه خرید با کد معرف:
```json
POST /api/purchases
{
    "plan_id": 1,
    "referral_code": "ABC12345",
    "payment_method": "online"
}
```

## ساختار دیتابیس

### جداول اصلی:
- `users` - کاربران سیستم
- `agents` - پروفایل نمایندگان (شامل اطلاعات کریپتو)
- `plans` - پلن‌های قابل خرید
- `purchases` - خریدهای انجام شده
- `commissions` - کمیسیون‌های محاسبه شده
- `withdrawal_requests` - درخواست‌های تسویه حساب
- `transactions` - تراکنش‌های مالی

### ارزهای پشتیبانی شده:
- Bitcoin (BTC)
- Ethereum (ETH)
- Tether (USDT)
- USD Coin (USDC)
- Binance Coin (BNB)
- TRON (TRX)

### شبکه‌های پشتیبانی شده:
- Bitcoin Network
- Ethereum (ERC20)
- TRON (TRC20)
- Binance Smart Chain (BEP20)
- Polygon Network

## امنیت

- احراز هویت چندمرحله‌ای با Laravel Sanctum
- محدودیت دسترسی بر اساس نقش
- اعتبارسنجی کامل آدرس‌های کیف پول
- رمزگذاری داده‌های حساس
- لاگ کامل تراکنش‌ها

## مشارکت در پروژه

1. Fork کنید
2. برنچ جدید ایجاد کنید
3. تغییرات را commit کنید
4. Pull Request ایجاد کنید

## لایسنس

این پروژه تحت لایسنس MIT منتشر شده است.
