<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->string('gateway_token')->nullable()->after('transaction_id'); // Token from payment gateway
            $table->string('gateway_order_id')->nullable()->after('gateway_token'); // Order ID for payment gateway
            $table->string('gateway_trans_id')->nullable()->after('gateway_order_id'); // Transaction ID from gateway
            $table->string('gateway_url')->nullable()->after('gateway_trans_id'); // Payment gateway URL
            $table->json('gateway_response')->nullable()->after('gateway_url'); // Full gateway response
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('purchases', function (Blueprint $table) {
            $table->dropColumn([
                'gateway_token',
                'gateway_order_id', 
                'gateway_trans_id',
                'gateway_url',
                'gateway_response'
            ]);
        });
    }
};
