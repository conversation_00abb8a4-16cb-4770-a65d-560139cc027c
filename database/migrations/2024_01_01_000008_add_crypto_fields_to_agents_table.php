<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->string('crypto_wallet_type')->nullable()->after('bank_name'); // BTC, ETH, USDT, etc.
            $table->string('crypto_wallet_address')->nullable()->after('crypto_wallet_type');
            $table->string('crypto_network')->nullable()->after('crypto_wallet_address'); // TRC20, ERC20, BEP20, etc.
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agents', function (Blueprint $table) {
            $table->dropColumn(['crypto_wallet_type', 'crypto_wallet_address', 'crypto_network']);
        });
    }
};
