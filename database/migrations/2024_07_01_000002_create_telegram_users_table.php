<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('telegram_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_bot_id')->constrained()->onDelete('cascade');
            $table->bigInteger('telegram_user_id');
            $table->string('telegram_username')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('phone_number')->nullable();
            $table->boolean('is_registered')->default(false);
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->timestamp('last_interaction')->nullable();
            $table->json('user_data')->nullable(); // برای ذخیره اطلاعات اضافی
            $table->timestamps();

            // Unique constraint for telegram_user_id per bot
            $table->unique(['telegram_bot_id', 'telegram_user_id']);
            
            // Index for better performance
            $table->index(['telegram_bot_id', 'last_interaction']);
            $table->index(['telegram_bot_id', 'is_registered']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('telegram_users');
    }
};
