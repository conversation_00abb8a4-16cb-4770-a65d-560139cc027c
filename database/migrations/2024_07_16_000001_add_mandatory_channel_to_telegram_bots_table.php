<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('telegram_bots', function (Blueprint $table) {
            $table->string('mandatory_channel_id')->nullable()->after('welcome_message');
            $table->string('mandatory_channel_username')->nullable()->after('mandatory_channel_id');
            $table->string('mandatory_channel_title')->nullable()->after('mandatory_channel_username');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('telegram_bots', function (Blueprint $table) {
            $table->dropColumn(['mandatory_channel_id', 'mandatory_channel_username', 'mandatory_channel_title']);
        });
    }
};
