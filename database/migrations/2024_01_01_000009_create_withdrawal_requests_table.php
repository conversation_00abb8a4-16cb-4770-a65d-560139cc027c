<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('withdrawal_requests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('agent_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 15, 2);
            $table->enum('method', ['bank', 'crypto'])->default('crypto');
            
            // Bank withdrawal fields
            $table->string('bank_account_number')->nullable();
            $table->string('bank_account_name')->nullable();
            $table->string('bank_name')->nullable();
            
            // Crypto withdrawal fields
            $table->string('crypto_wallet_type')->nullable(); // BTC, ETH, USDT, etc.
            $table->string('crypto_wallet_address')->nullable();
            $table->string('crypto_network')->nullable(); // TRC20, ERC20, BEP20, etc.
            
            $table->enum('status', ['pending', 'approved', 'processing', 'completed', 'rejected'])->default('pending');
            $table->text('admin_notes')->nullable();
            $table->text('agent_notes')->nullable();
            $table->string('transaction_hash')->nullable(); // برای تراکنش‌های کریپتو
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('withdrawal_requests');
    }
};
