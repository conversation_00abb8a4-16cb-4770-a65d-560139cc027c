<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Agent;
use App\Models\Plan;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'مدیر سیستم',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => User::ROLE_ADMIN,
            'is_active' => true,
        ]);

        // Create sample agent
        $agentUser = User::create([
            'name' => 'نماینده نمونه',
            'email' => '<EMAIL>',
            'phone' => '***********',
            'password' => Hash::make('password'),
            'role' => User::ROLE_AGENT,
            'is_active' => true,
        ]);

        $agent = Agent::create([
            'user_id' => $agentUser->id,
            'commission_rate' => 15.00,
            'bank_account_number' => '**********',
            'bank_account_name' => 'نماینده نمونه',
            'bank_name' => 'بانک ملی',
            'crypto_wallet_type' => 'USDT',
            'crypto_wallet_address' => 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
            'crypto_network' => 'TRC20',
            'is_active' => true,
        ]);

        // Create sample customer
        $customer = User::create([
            'name' => 'مشتری نمونه',
            'email' => '<EMAIL>',
            'phone' => '***********',
            'password' => Hash::make('password'),
            'role' => User::ROLE_CUSTOMER,
            'is_active' => true,
        ]);

        // Create sample plans
        Plan::create([
            'name' => 'پلن پایه',
            'type' => 'اشتراک',
            'description' => 'پلن پایه برای شروع کار',
            'post_purchase_description' => 'تبریک! شما با موفقیت پلن پایه را خریداری کردید. اکنون می‌توانید از پنل کاربری و پشتیبانی ۲۴ ساعته استفاده کنید.',
            'link' => 'https://panel.example.com/basic',
            'price' => 100000,
            'duration_days' => 30,
            'features' => [
                'دسترسی به پنل کاربری',
                'پشتیبانی 24 ساعته',
                'آپدیت رایگان'
            ],
            'status' => 'active',
            'sort_order' => 1,
        ]);

        Plan::create([
            'name' => 'پلن حرفه‌ای',
            'type' => 'اشتراک کسب‌وکار',
            'description' => 'پلن حرفه‌ای برای کسب‌وکارها',
            'post_purchase_description' => 'عالی! پلن حرفه‌ای شما فعال شد. اکنون دسترسی به گزارش‌گیری پیشرفته، API و پشتیبانی اولویت‌دار دارید.',
            'link' => 'https://panel.example.com/professional',
            'price' => 250000,
            'duration_days' => 90,
            'features' => [
                'تمام امکانات پلن پایه',
                'گزارش‌گیری پیشرفته',
                'API دسترسی',
                'پشتیبانی اولویت‌دار'
            ],
            'status' => 'active',
            'sort_order' => 2,
        ]);

        Plan::create([
            'name' => 'پلن طلایی',
            'type' => 'اشتراک VIP',
            'description' => 'پلن کامل برای شرکت‌های بزرگ',
            'post_purchase_description' => 'فوق‌العاده! شما عضو VIP ما شدید. از مشاوره تخصصی، سفارشی‌سازی کامل، پشتیبانی اختصاصی و آموزش حضوری بهره‌مند شوید.',
            'link' => 'https://panel.example.com/gold',
            'price' => 500000,
            'duration_days' => 365,
            'features' => [
                'تمام امکانات پلن‌های قبلی',
                'مشاوره تخصصی',
                'سفارشی‌سازی کامل',
                'پشتیبانی اختصاصی',
                'آموزش حضوری'
            ],
            'status' => 'active',
            'sort_order' => 3,
        ]);

        echo "✅ داده‌های اولیه با موفقیت ایجاد شد:\n";
        echo "👤 ادمین: <EMAIL> / password\n";
        echo "🤝 نماینده: <EMAIL> / password (کد معرف: {$agent->referral_code})\n";
        echo "👥 مشتری: <EMAIL> / password\n";
        echo "📦 3 پلن نمونه ایجاد شد\n";
    }
}
