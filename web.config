<?xml version="1.0"?>
<!--
    Rewrites requires Microsoft URL Rewrite Module for IIS
    Download: https://www.microsoft.com/en-us/download/details.aspx?id=47337
    Debug Help: https://docs.microsoft.com/en-us/iis/extensions/url-rewrite-module/using-failed-request-tracing-to-trace-rewrite-rules
-->
<configuration>
  <system.webServer>
    <!-- CORS Configuration -->
    <httpProtocol>
      <customHeaders>
        <remove name="Access-Control-Allow-Origin" />
        <remove name="Access-Control-Allow-Methods" />
        <remove name="Access-Control-Allow-Headers" />
        <remove name="Access-Control-Max-Age" />
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS, PATCH, HEAD" />
        <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization, X-Requested-With, Accept, Origin, X-CSRF-TOKEN" />
        <add name="Access-Control-Max-Age" value="3600" />
      </customHeaders>
    </httpProtocol>
    
    <rewrite>
      <rules>
        <!-- Handle OPTIONS requests before other rules -->
        <rule name="Handle OPTIONS" stopProcessing="true">
          <match url=".*" />
          <conditions>
            <add input="{REQUEST_METHOD}" pattern="OPTIONS" ignoreCase="true" />
          </conditions>
          <action type="CustomResponse" statusCode="200" statusReason="OK" statusDescription="OK" />
        </rule>
        
        <rule name="Imported Rule 1" stopProcessing="true">
          <match url="^(.*)/$" ignoreCase="false" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
          </conditions>
          <action type="Redirect" redirectType="Permanent" url="/{R:1}" />
        </rule>
        <rule name="Imported Rule 2" stopProcessing="true">
          <match url="^" ignoreCase="false" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.php" />
        </rule>
      </rules>
    </rewrite>
    <tracing>
      <traceFailedRequests>
        <clear />
      </traceFailedRequests>
    </tracing>
    <httpErrors>
      <remove statusCode="400" />
      <error statusCode="400" path="E:\site\nitropardazesh.site\error_docs\bad_request.html" />
      <remove statusCode="401" />
      <error statusCode="401" path="E:\site\nitropardazesh.site\error_docs\unauthorized.html" />
      <remove statusCode="403" />
      <error statusCode="403" path="E:\site\nitropardazesh.site\error_docs\forbidden.html" />
      <remove statusCode="404" />
      <error statusCode="404" path="E:\site\nitropardazesh.site\error_docs\not_found.html" />
      <remove statusCode="405" />
      <error statusCode="405" path="E:\site\nitropardazesh.site\error_docs\method_not_allowed.html" />
      <remove statusCode="406" />
      <error statusCode="406" path="E:\site\nitropardazesh.site\error_docs\not_acceptable.html" />
      <remove statusCode="407" />
      <error statusCode="407" path="E:\site\nitropardazesh.site\error_docs\proxy_authentication_required.html" />
      <remove statusCode="412" />
      <error statusCode="412" path="E:\site\nitropardazesh.site\error_docs\precondition_failed.html" />
      <remove statusCode="414" />
      <error statusCode="414" path="E:\site\nitropardazesh.site\error_docs\request-uri_too_long.html" />
      <remove statusCode="415" />
      <error statusCode="415" path="E:\site\nitropardazesh.site\error_docs\unsupported_media_type.html" />
      <remove statusCode="500" />
      <error statusCode="500" path="E:\site\nitropardazesh.site\error_docs\internal_server_error.html" />
      <remove statusCode="501" />
      <error statusCode="501" path="E:\site\nitropardazesh.site\error_docs\not_implemented.html" />
      <remove statusCode="502" />
      <error statusCode="502" path="E:\site\nitropardazesh.site\error_docs\bad_gateway.html" />
      <remove statusCode="503" />
      <error statusCode="503" path="E:\site\nitropardazesh.site\error_docs\maintenance.html" />
    </httpErrors>
  </system.webServer>
  <system.web>
    <compilation tempDirectory="E:\site\nitropardazesh.site\tmp" />
  </system.web>
</configuration>
