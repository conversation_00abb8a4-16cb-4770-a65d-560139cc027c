<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WithdrawalRequest;
use App\Services\WithdrawalService;

class ProcessWithdrawals extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'withdrawals:process 
                            {--dry-run : Show what would be processed without making changes}
                            {--auto-approve : Automatically approve small amounts}
                            {--max-amount=100000 : Maximum amount to auto-approve}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending withdrawal requests';

    protected $withdrawalService;

    public function __construct(WithdrawalService $withdrawalService)
    {
        parent::__construct();
        $this->withdrawalService = $withdrawalService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 Processing withdrawal requests...');
        
        $dryRun = $this->option('dry-run');
        $autoApprove = $this->option('auto-approve');
        $maxAmount = (float) $this->option('max-amount');

        // Get pending withdrawals
        $pendingWithdrawals = WithdrawalRequest::with(['agent.user'])
            ->pending()
            ->orderBy('created_at')
            ->get();

        if ($pendingWithdrawals->isEmpty()) {
            $this->info('✅ No pending withdrawal requests found.');
            return;
        }

        $this->info("📋 Found {$pendingWithdrawals->count()} pending withdrawal requests:");
        
        $table = [];
        $autoApproveCount = 0;
        $totalAutoApproveAmount = 0;

        foreach ($pendingWithdrawals as $withdrawal) {
            $canAutoApprove = $autoApprove && 
                             $withdrawal->amount <= $maxAmount && 
                             $withdrawal->agent->wallet_balance >= $withdrawal->amount;

            $table[] = [
                'ID' => $withdrawal->id,
                'Agent' => $withdrawal->agent->user->name,
                'Amount' => number_format($withdrawal->amount) . ' تومان',
                'Method' => $withdrawal->method_in_persian,
                'Created' => $withdrawal->created_at->format('Y/m/d H:i'),
                'Auto Approve' => $canAutoApprove ? '✅' : '❌',
            ];

            if ($canAutoApprove) {
                $autoApproveCount++;
                $totalAutoApproveAmount += $withdrawal->amount;
            }
        }

        $this->table([
            'ID', 'Agent', 'Amount', 'Method', 'Created', 'Auto Approve'
        ], $table);

        if ($autoApprove && $autoApproveCount > 0) {
            $this->info("🤖 Auto-approve summary:");
            $this->info("   • Count: {$autoApproveCount} requests");
            $this->info("   • Total: " . number_format($totalAutoApproveAmount) . " تومان");
            
            if (!$dryRun) {
                if ($this->confirm('Do you want to proceed with auto-approval?')) {
                    $this->processAutoApprovals($pendingWithdrawals, $maxAmount);
                }
            } else {
                $this->warn('🔍 DRY RUN: No changes will be made.');
            }
        }

        // Show statistics
        $this->showStatistics();
    }

    private function processAutoApprovals($withdrawals, $maxAmount)
    {
        $approved = 0;
        $failed = 0;

        foreach ($withdrawals as $withdrawal) {
            if ($withdrawal->amount <= $maxAmount && 
                $withdrawal->agent->wallet_balance >= $withdrawal->amount) {
                
                if ($this->withdrawalService->approveWithdrawal($withdrawal, 'Auto-approved by system')) {
                    $approved++;
                    $this->info("✅ Approved withdrawal #{$withdrawal->id} for {$withdrawal->agent->user->name}");
                } else {
                    $failed++;
                    $this->error("❌ Failed to approve withdrawal #{$withdrawal->id}");
                }
            }
        }

        $this->info("📊 Auto-approval results:");
        $this->info("   • Approved: {$approved}");
        $this->info("   • Failed: {$failed}");
    }

    private function showStatistics()
    {
        $stats = $this->withdrawalService->getWithdrawalStats();
        
        $this->info("\n📈 Withdrawal Statistics:");
        $this->info("   • Pending: {$stats['pending_count']} requests (" . number_format($stats['pending_amount']) . " تومان)");
        $this->info("   • Completed: {$stats['completed_count']} requests (" . number_format($stats['completed_amount']) . " تومان)");
        $this->info("   • Crypto pending: " . number_format($stats['crypto_pending']) . " تومان");
        $this->info("   • Bank pending: " . number_format($stats['bank_pending']) . " تومان");
        $this->info("   • This month completed: " . number_format($stats['this_month_completed']) . " تومان");
    }
}
