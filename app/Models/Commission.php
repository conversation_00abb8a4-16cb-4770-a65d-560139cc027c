<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Commission extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'agent_id',
        'purchase_id',
        'amount',
        'status',
        'paid_at',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the agent who earned this commission
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the purchase that generated this commission
     */
    public function purchase()
    {
        return $this->belongsTo(Purchase::class);
    }

    /**
     * Scope for pending commissions
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for paid commissions
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * Check if commission is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if commission is paid
     */
    public function isPaid(): bool
    {
        return $this->status === self::STATUS_PAID;
    }

    /**
     * Mark commission as paid
     */
    public function markAsPaid($notes = null)
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_at' => now(),
            'notes' => $notes,
        ]);

        // Update agent's wallet balance
        $this->agent->increment('wallet_balance', $this->amount);
        $this->agent->increment('total_commission', $this->amount);

        // Create transaction record
        Transaction::create([
            'user_id' => $this->agent->user_id,
            'agent_id' => $this->agent_id,
            'type' => Transaction::TYPE_COMMISSION,
            'amount' => $this->amount,
            'description' => 'کمیسیون فروش پلن ' . $this->purchase->plan->name,
            'reference_id' => $this->id,
        ]);
    }

    /**
     * Cancel commission
     */
    public function cancel($notes = null)
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
            'notes' => $notes,
        ]);
    }
}
