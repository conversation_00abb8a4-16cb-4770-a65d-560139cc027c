<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TelegramBot extends Model
{
    use HasFactory;

    protected $fillable = [
        'agent_id',
        'bot_token',
        'bot_username',
        'webhook_url',
        'welcome_message',
        'is_active',
        'webhook_set_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'webhook_set_at' => 'datetime',
    ];

    /**
     * Get the agent that owns this telegram bot
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get telegram users for this bot
     */
    public function telegramUsers()
    {
        return $this->hasMany(TelegramUser::class);
    }

    /**
     * Get default welcome message
     */
    public function getWelcomeMessage(): string
    {
        return $this->welcome_message ?: 
            "سلام! 👋 به ربات {$this->agent->user->name} خوش آمدید.\n\n" .
            "🔹 برای مشاهده پلن‌های موجود: /plans\n" .
            "🔹 برای راهنما: /help\n" .
            "🔹 برای ثبت‌نام در سایت: /register\n\n" .
            "کد معرف شما: {$this->agent->referral_code}";
    }

    /**
     * Get webhook URL for this bot
     */
    public function getWebhookUrl(): string
    {
        return url("/api/telegram/webhook/{$this->id}");
    }

    /**
     * Check if webhook is set
     */
    public function hasWebhook(): bool
    {
        return !is_null($this->webhook_set_at);
    }

    /**
     * Get bot API URL
     */
    public function getBotApiUrl(): string
    {
        return "https://api.telegram.org/bot{$this->bot_token}";
    }

    /**
     * Get total users count
     */
    public function getTotalUsersCount(): int
    {
        return $this->telegramUsers()->count();
    }

    /**
     * Get active users count (interacted in last 30 days)
     */
    public function getActiveUsersCount(): int
    {
        return $this->telegramUsers()
            ->where('last_interaction', '>=', now()->subDays(30))
            ->count();
    }

    /**
     * Get registered users count (users who registered on website)
     */
    public function getRegisteredUsersCount(): int
    {
        return $this->telegramUsers()
            ->where('is_registered', true)
            ->count();
    }
}
