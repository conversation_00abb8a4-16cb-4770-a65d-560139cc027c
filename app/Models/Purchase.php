<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Purchase extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';

    protected $fillable = [
        'user_id',
        'plan_id',
        'agent_id',
        'amount',
        'commission_amount',
        'status',
        'payment_method',
        'transaction_id',
        'gateway_token',
        'gateway_order_id',
        'gateway_trans_id',
        'gateway_url',
        'gateway_response',
        'expires_at',
        'activated_at',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'gateway_response' => 'array',
        'expires_at' => 'datetime',
        'activated_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($purchase) {
            if ($purchase->plan && !$purchase->expires_at) {
                $purchase->expires_at = now()->addDays($purchase->plan->duration_days);
            }
        });
    }

    /**
     * Get the user who made this purchase
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan that was purchased
     */
    public function plan()
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the agent who referred this purchase
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Get the commission record for this purchase
     */
    public function commission()
    {
        return $this->hasOne(Commission::class);
    }

    /**
     * Scope for completed purchases
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for active purchases (not expired)
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_COMPLETED)
                    ->where('expires_at', '>', now());
    }

    /**
     * Check if purchase is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if purchase is active (completed and not expired)
     */
    public function isActive(): bool
    {
        return $this->isCompleted() && $this->expires_at > now();
    }

    /**
     * Check if purchase is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now();
    }

    /**
     * Mark purchase as completed and activate
     */
    public function markAsCompleted()
    {
        $this->update([
            'status' => self::STATUS_COMPLETED,
            'activated_at' => now(),
        ]);

        // Create commission record if agent exists
        if ($this->agent_id && $this->commission_amount > 0) {
            Commission::create([
                'agent_id' => $this->agent_id,
                'purchase_id' => $this->id,
                'amount' => $this->commission_amount,
                'status' => Commission::STATUS_PENDING,
            ]);
        }
    }

    /**
     * Get remaining days
     */
    public function getRemainingDaysAttribute(): int
    {
        if (!$this->isActive()) {
            return 0;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }

    /**
     * Scope for expired purchases
     */
    public function scopeExpired($query)
    {
        return $query->where('status', self::STATUS_COMPLETED)
                    ->where('expires_at', '<=', now());
    }
}
