<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WithdrawalRequest extends Model
{
    use HasFactory;

    const STATUS_PENDING = 'pending';
    const STATUS_APPROVED = 'approved';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_REJECTED = 'rejected';

    const METHOD_BANK = 'bank';
    const METHOD_CRYPTO = 'crypto';

    // Supported crypto types
    const CRYPTO_TYPES = [
        'BTC' => 'Bitcoin',
        'ETH' => 'Ethereum',
        'USDT' => 'Tether',
        'USDC' => 'USD Coin',
        'BNB' => 'Binance Coin',
        'TRX' => 'TRON',
    ];

    // Supported networks
    const CRYPTO_NETWORKS = [
        'BTC' => 'Bitcoin Network',
        'ETH' => 'Ethereum (ERC20)',
        'TRC20' => 'TRON (TRC20)',
        'BEP20' => 'Binance Smart Chain (BEP20)',
        'POLYGON' => 'Polygon Network',
    ];

    protected $fillable = [
        'agent_id',
        'amount',
        'method',
        'bank_account_number',
        'bank_account_name',
        'bank_name',
        'crypto_wallet_type',
        'crypto_wallet_address',
        'crypto_network',
        'status',
        'admin_notes',
        'agent_notes',
        'transaction_hash',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the agent that owns this withdrawal request
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope for approved requests
     */
    public function scopeApproved($query)
    {
        return $query->where('status', self::STATUS_APPROVED);
    }

    /**
     * Scope for completed requests
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope for crypto withdrawals
     */
    public function scopeCrypto($query)
    {
        return $query->where('method', self::METHOD_CRYPTO);
    }

    /**
     * Scope for bank withdrawals
     */
    public function scopeBank($query)
    {
        return $query->where('method', self::METHOD_BANK);
    }

    /**
     * Check if request is pending
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if request is approved
     */
    public function isApproved(): bool
    {
        return $this->status === self::STATUS_APPROVED;
    }

    /**
     * Check if request is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if request is rejected
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Check if request is crypto withdrawal
     */
    public function isCrypto(): bool
    {
        return $this->method === self::METHOD_CRYPTO;
    }

    /**
     * Check if request is bank withdrawal
     */
    public function isBank(): bool
    {
        return $this->method === self::METHOD_BANK;
    }

    /**
     * Get status in Persian
     */
    public function getStatusInPersianAttribute(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'در انتظار بررسی',
            self::STATUS_APPROVED => 'تایید شده',
            self::STATUS_PROCESSING => 'در حال پردازش',
            self::STATUS_COMPLETED => 'تکمیل شده',
            self::STATUS_REJECTED => 'رد شده',
            default => $this->status,
        };
    }

    /**
     * Get method in Persian
     */
    public function getMethodInPersianAttribute(): string
    {
        return match($this->method) {
            self::METHOD_BANK => 'حساب بانکی',
            self::METHOD_CRYPTO => 'کیف پول کریپتو',
            default => $this->method,
        };
    }

    /**
     * Get formatted amount
     */
    public function getFormattedAmountAttribute(): string
    {
        return number_format($this->amount, 0) . ' تومان';
    }

    /**
     * Get crypto type name
     */
    public function getCryptoTypeNameAttribute(): string
    {
        return self::CRYPTO_TYPES[$this->crypto_wallet_type] ?? $this->crypto_wallet_type;
    }

    /**
     * Get network name
     */
    public function getNetworkNameAttribute(): string
    {
        return self::CRYPTO_NETWORKS[$this->crypto_network] ?? $this->crypto_network;
    }
}
