<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Plan extends Model
{
    use HasFactory;

    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    const TYPE_SUBSCRIPTION = 'subscription';
    const TYPE_PRODUCT = 'product';
    const TYPE_SERVICE = 'service';
    const TYPE_COURSE = 'course';
    const TYPE_SOFTWARE = 'software';

    const TYPES = [
        self::TYPE_SUBSCRIPTION => 'اشتراک',
        self::TYPE_PRODUCT => 'محصول',
        self::TYPE_SERVICE => 'خدمات',
        self::TYPE_COURSE => 'دوره آموزشی',
        self::TYPE_SOFTWARE => 'نرم‌افزار',
    ];

    protected $fillable = [
        'name',
        'type',
        'description',
        'post_purchase_description',
        'link',
        'banner',
        'price',
        'duration_days',
        'features',
        'status',
        'sort_order',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'features' => 'array',
        'duration_days' => 'integer',
        'sort_order' => 'integer',
    ];

    /**
     * Scope for active plans
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope for ordering plans
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Get purchases for this plan
     */
    public function purchases()
    {
        return $this->hasMany(Purchase::class);
    }

    /**
     * Check if plan is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Get formatted price
     */
    public function getFormattedPriceAttribute(): string
    {
        return number_format($this->price, 0) . ' تومان';
    }

    /**
     * Get duration in human readable format
     */
    public function getFormattedDurationAttribute(): string
    {
        if ($this->duration_days == 30) {
            return '1 ماه';
        } elseif ($this->duration_days == 90) {
            return '3 ماه';
        } elseif ($this->duration_days == 365) {
            return '1 سال';
        }

        return $this->duration_days . ' روز';
    }

    /**
     * Get banner URL
     */
    public function getBannerUrlAttribute(): ?string
    {
        return $this->banner ? asset('storage/' . $this->banner) : null;
    }
}
