<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TelegramUser extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_bot_id',
        'telegram_user_id',
        'telegram_username',
        'first_name',
        'last_name',
        'phone_number',
        'is_registered',
        'user_id',
        'last_interaction',
        'user_data',
    ];

    protected $casts = [
        'is_registered' => 'boolean',
        'last_interaction' => 'datetime',
        'user_data' => 'array',
        'telegram_user_id' => 'integer',
    ];

    /**
     * Get the telegram bot that this user belongs to
     */
    public function telegramBot()
    {
        return $this->belongsTo(TelegramBot::class);
    }

    /**
     * Get the registered user (if exists)
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the agent through telegram bot
     */
    public function agent()
    {
        return $this->telegramBot->agent();
    }

    /**
     * Get full name
     */
    public function getFullName(): string
    {
        $name = trim(($this->first_name ?? '') . ' ' . ($this->last_name ?? ''));
        return $name ?: $this->telegram_username ?: 'کاربر تلگرام';
    }

    /**
     * Update last interaction timestamp
     */
    public function updateLastInteraction()
    {
        $this->update(['last_interaction' => now()]);
    }

    /**
     * Check if user is active (interacted in last 30 days)
     */
    public function isActive(): bool
    {
        return $this->last_interaction && 
               $this->last_interaction->greaterThan(now()->subDays(30));
    }

    /**
     * Mark user as registered and link to website user
     */
    public function markAsRegistered(User $user)
    {
        $this->update([
            'is_registered' => true,
            'user_id' => $user->id,
        ]);

        // Also update the website user with telegram info if not set
        if (!$user->referred_by_agent_id) {
            $user->update([
                'referred_by_agent_id' => $this->telegramBot->agent_id,
            ]);
        }
    }

    /**
     * Get user data value
     */
    public function getUserData(string $key, $default = null)
    {
        return data_get($this->user_data, $key, $default);
    }

    /**
     * Set user data value
     */
    public function setUserData(string $key, $value)
    {
        $userData = $this->user_data ?? [];
        data_set($userData, $key, $value);
        $this->update(['user_data' => $userData]);
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('last_interaction', '>=', now()->subDays(30));
    }

    /**
     * Scope for registered users
     */
    public function scopeRegistered($query)
    {
        return $query->where('is_registered', true);
    }
}
