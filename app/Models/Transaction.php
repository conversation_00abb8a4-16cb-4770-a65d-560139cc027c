<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;

    const TYPE_COMMISSION = 'commission';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_PURCHASE = 'purchase';
    const TYPE_REFUND = 'refund';

    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    protected $fillable = [
        'user_id',
        'agent_id',
        'type',
        'amount',
        'status',
        'description',
        'reference_id',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
    ];

    /**
     * Get the user associated with this transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the agent associated with this transaction
     */
    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }

    /**
     * Scope for commission transactions
     */
    public function scopeCommissions($query)
    {
        return $query->where('type', self::TYPE_COMMISSION);
    }

    /**
     * Scope for withdrawal transactions
     */
    public function scopeWithdrawals($query)
    {
        return $query->where('type', self::TYPE_WITHDRAWAL);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Check if transaction is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Get formatted amount with sign
     */
    public function getFormattedAmountAttribute(): string
    {
        $sign = in_array($this->type, [self::TYPE_COMMISSION]) ? '+' : '-';
        return $sign . number_format($this->amount, 0) . ' تومان';
    }

    /**
     * Get transaction type in Persian
     */
    public function getTypeInPersianAttribute(): string
    {
        return match($this->type) {
            self::TYPE_COMMISSION => 'کمیسیون',
            self::TYPE_WITHDRAWAL => 'برداشت',
            self::TYPE_PURCHASE => 'خرید',
            self::TYPE_REFUND => 'بازگشت وجه',
            default => $this->type,
        };
    }
}
