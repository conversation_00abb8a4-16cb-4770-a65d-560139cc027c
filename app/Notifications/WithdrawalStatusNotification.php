<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\WithdrawalRequest;

class WithdrawalStatusNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $withdrawal;
    protected $status;

    /**
     * Create a new notification instance.
     */
    public function __construct(WithdrawalRequest $withdrawal, string $status)
    {
        $this->withdrawal = $withdrawal;
        $this->status = $status;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = $this->getSubject();
        $greeting = "سلام {$notifiable->name}";
        
        $mailMessage = (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($this->getMainMessage())
            ->line("شناسه درخواست: #{$this->withdrawal->id}")
            ->line("مبلغ: {$this->withdrawal->formatted_amount}")
            ->line("روش پرداخت: {$this->withdrawal->method_in_persian}");

        if ($this->withdrawal->admin_notes) {
            $mailMessage->line("توضیحات ادمین: {$this->withdrawal->admin_notes}");
        }

        if ($this->withdrawal->isCompleted() && $this->withdrawal->transaction_hash) {
            $mailMessage->line("هش تراکنش: {$this->withdrawal->transaction_hash}");
        }

        $mailMessage->action('مشاهده جزئیات', route('agent.withdrawals.show', $this->withdrawal))
                   ->line('با تشکر از همکاری شما');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'withdrawal_id' => $this->withdrawal->id,
            'status' => $this->status,
            'amount' => $this->withdrawal->amount,
            'method' => $this->withdrawal->method,
            'title' => $this->getSubject(),
            'message' => $this->getMainMessage(),
            'admin_notes' => $this->withdrawal->admin_notes,
            'transaction_hash' => $this->withdrawal->transaction_hash,
        ];
    }

    private function getSubject(): string
    {
        return match($this->status) {
            'approved' => 'درخواست تسویه حساب شما تایید شد',
            'rejected' => 'درخواست تسویه حساب شما رد شد',
            'processing' => 'درخواست تسویه حساب شما در حال پردازش است',
            'completed' => 'درخواست تسویه حساب شما تکمیل شد',
            default => 'وضعیت درخواست تسویه حساب شما تغییر کرد',
        };
    }

    private function getMainMessage(): string
    {
        return match($this->status) {
            'approved' => 'درخواست تسویه حساب شما مورد تایید قرار گرفت و به زودی پردازش خواهد شد.',
            'rejected' => 'متأسفانه درخواست تسویه حساب شما رد شد. لطفاً توضیحات ادمین را مطالعه کنید.',
            'processing' => 'درخواست تسویه حساب شما در حال پردازش است. لطفاً صبور باشید.',
            'completed' => 'درخواست تسویه حساب شما با موفقیت تکمیل شد و مبلغ به حساب شما واریز گردید.',
            default => 'وضعیت درخواست تسویه حساب شما تغییر کرده است.',
        };
    }
}
