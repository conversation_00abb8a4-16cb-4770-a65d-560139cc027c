<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PlanResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'description' => $this->description,
            'post_purchase_description' => $this->post_purchase_description,
            'link' => $this->link,
            'banner' => $this->banner,
            'banner_url' => $this->banner_url,
            'price' => [
                'amount' => $this->price,
                'formatted' => $this->formatted_price,
                'currency' => 'تومان'
            ],
            'duration' => [
                'days' => $this->duration_days,
                'formatted' => $this->formatted_duration
            ],
            'features' => $this->features ?? [],
            'status' => $this->status,
            'sort_order' => $this->sort_order,
            'is_active' => $this->isActive(),
            'timestamps' => [
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
            ],
            // Statistics (only when requested)
            'statistics' => $this->when($request->has('include_stats'), function () {
                return [
                    'total_purchases' => $this->purchases()->count(),
                    'active_subscriptions' => $this->purchases()->active()->count(),
                    'total_revenue' => $this->purchases()->where('status', 'completed')->sum('amount'),
                ];
            }),
        ];
    }
}
