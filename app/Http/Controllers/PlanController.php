<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    /**
     * Display public plans page
     */
    public function index(Request $request)
    {
        $plans = Plan::active()
            ->ordered()
            ->get();

        return view('plans.index', compact('plans'));
    }

    /**
     * Show specific plan details
     */
    public function show(Plan $plan)
    {
        if (!$plan->isActive()) {
            abort(404);
        }

        return view('plans.show', compact('plan'));
    }
}
