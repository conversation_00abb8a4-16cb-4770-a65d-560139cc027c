<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Handle payment gateway callback
     */
    public function callback(Request $request)
    {
        Log::info('Payment callback received', $request->all());

        try {
            $result = $this->paymentService->verifyPayment($request->all());

            if ($result['success']) {
                // Redirect to success page or return success response
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => [
                        'purchase_id' => $result['purchase']->id,
                        'status' => $result['purchase']->status,
                    ]
                ]);
            } else {
                // Redirect to failure page or return error response
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Payment callback error', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'خطا در پردازش پاسخ درگاه پرداخت'
            ], 500);
        }
    }

    /**
     * Check payment status
     */
    public function status(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
        ]);

        $purchase = \App\Models\Purchase::where('gateway_token', $request->token)->first();

        if (!$purchase) {
            return response()->json([
                'success' => false,
                'message' => 'تراکنش یافت نشد'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'purchase_id' => $purchase->id,
                'status' => $purchase->status,
                'amount' => $purchase->amount,
                'plan_name' => $purchase->plan->name,
                'created_at' => $purchase->created_at,
                'gateway_response' => $purchase->gateway_response,
            ]
        ]);
    }

    /**
     * Test payment gateway connection
     */
    public function test(Request $request)
    {
        if (!config('app.debug')) {
            return response()->json([
                'success' => false,
                'message' => 'این endpoint فقط در حالت debug در دسترس است'
            ], 403);
        }

        $testData = [
            'Api_key' => config('services.payment_gateway.api_key'),
            'user_id' => 1,
            'email' => '<EMAIL>',
            'amount' => 1000,
            'description' => 'تست اتصال درگاه',
            'callback' => config('services.payment_gateway.callback_url'),
            'invoice_id' => time(),
        ];

        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, config('services.payment_gateway.url'));
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($testData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded',
                'Accept: application/json',
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            return response()->json([
                'success' => true,
                'data' => [
                    'request_data' => $testData,
                    'response' => $response,
                    'http_code' => $httpCode,
                    'parsed_response' => json_decode($response, true)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در تست درگاه: ' . $e->getMessage(),
                'data' => [
                    'request_data' => $testData,
                    'error' => $e->getMessage()
                ]
            ], 500);
        }
    }
}
