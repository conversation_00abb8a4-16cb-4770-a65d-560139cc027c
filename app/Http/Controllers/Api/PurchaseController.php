<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Purchase;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    public function index(Request $request)
    {
        $purchases = $request->user()
            ->purchases()
            ->with('plan')
            ->latest()
            ->paginate(15);

        $data = $purchases->getCollection()->map(function ($purchase) {
            return [
                'id' => $purchase->id,
                'plan' => [
                    'id' => $purchase->plan->id,
                    'name' => $purchase->plan->name,
                    'price' => $purchase->plan->price,
                ],
                'amount' => $purchase->amount,
                'status' => $purchase->status,
                'expires_at' => $purchase->expires_at,
                'activated_at' => $purchase->activated_at,
                'remaining_days' => $purchase->remaining_days,
                'is_active' => $purchase->isActive(),
                'created_at' => $purchase->created_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'purchases' => $data,
                'pagination' => [
                    'current_page' => $purchases->currentPage(),
                    'last_page' => $purchases->lastPage(),
                    'per_page' => $purchases->perPage(),
                    'total' => $purchases->total(),
                ]
            ]
        ]);
    }

    public function store(Request $request, PaymentService $paymentService)
    {
        $validator = Validator::make($request->all(), [
            'plan_id' => 'required|exists:plans,id',
            'payment_method' => 'required|string|in:online,card,cash',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'خطا در اعتبارسنجی داده‌ها',
                'errors' => $validator->errors()
            ], 422);
        }

        $plan = Plan::find($request->plan_id);
        
        if (!$plan->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'پلن مورد نظر در دسترس نیست'
            ], 400);
        }

        // Get user's referring agent
        $agent = $request->user()->referredByAgent;
        $commissionAmount = 0;

        if ($agent && $agent->is_active) {
            $commissionAmount = ($plan->price * $agent->commission_rate) / 100;
        }

        try {
            DB::beginTransaction();

            $purchase = Purchase::create([
                'user_id' => $request->user()->id,
                'plan_id' => $plan->id,
                'agent_id' => $agent?->id,
                'amount' => $plan->price,
                'commission_amount' => $commissionAmount,
                'status' => Purchase::STATUS_PENDING,
                'payment_method' => $request->payment_method,
                'transaction_id' => 'TXN_' . time() . '_' . rand(1000, 9999),
            ]);

            // For online payment, create payment gateway request
            if ($request->payment_method === 'online') {
                $paymentResult = $paymentService->createPaymentRequest($purchase);

                if (!$paymentResult['success']) {
                    DB::rollBack();
                    $response = [
                        'success' => false,
                        'message' => $paymentResult['message']
                    ];

                    // Add debug info in development
                    if (config('app.debug')) {
                        $response['debug'] = $paymentResult['debug'] ?? null;
                    }

                    return response()->json($response, 400);
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'درخواست پرداخت ایجاد شد',
                    'data' => [
                        'purchase' => [
                            'id' => $purchase->id,
                            'plan' => [
                                'id' => $plan->id,
                                'name' => $plan->name,
                                'price' => $plan->price,
                            ],
                            'amount' => $purchase->amount,
                            'status' => $purchase->status,
                            'transaction_id' => $purchase->transaction_id,
                            'agent' => $agent ? [
                                'name' => $agent->user->name,
                                'referral_code' => $agent->referral_code
                            ] : null
                        ],
                        'payment_url' => "https://mainpay.nitropardazesh.site/startpay/".$paymentResult['payment_url'],
                        'gateway_token' => $paymentResult['token'],
                    ]
                ], 201);
            } else {
                // For other payment methods, mark as completed (manual verification needed)
                $purchase->markAsCompleted();

                // Update agent statistics
                if ($agent) {
                    $agent->increment('total_sales', $plan->price);
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'خرید با موفقیت انجام شد',
                    'data' => [
                        'purchase' => [
                            'id' => $purchase->id,
                            'plan' => [
                                'id' => $plan->id,
                                'name' => $plan->name,
                                'price' => $plan->price,
                            ],
                            'amount' => $purchase->amount,
                            'status' => $purchase->status,
                            'expires_at' => $purchase->expires_at,
                            'transaction_id' => $purchase->transaction_id,
                            'agent' => $agent ? [
                                'name' => $agent->user->name,
                                'referral_code' => $agent->referral_code
                            ] : null
                        ]
                    ]
                ], 201);
            }

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'خطا در انجام خرید'
            ], 500);
        }
    }

    public function show(Request $request, Purchase $purchase)
    {
        if ($purchase->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'دسترسی غیرمجاز'
            ], 403);
        }

        $purchase->load('plan');

        return response()->json([
            'success' => true,
            'data' => [
                'purchase' => [
                    'id' => $purchase->id,
                    'plan' => [
                        'id' => $purchase->plan->id,
                        'name' => $purchase->plan->name,
                        'price' => $purchase->plan->price,
                        'features' => $purchase->plan->features,
                    ],
                    'amount' => $purchase->amount,
                    'status' => $purchase->status,
                    'expires_at' => $purchase->expires_at,
                    'activated_at' => $purchase->activated_at,
                    'remaining_days' => $purchase->remaining_days,
                    'is_active' => $purchase->isActive(),
                    'transaction_id' => $purchase->transaction_id,
                    'created_at' => $purchase->created_at,
                ]
            ]
        ]);
    }

    /**
     * Get user's active plans
     */
    public function myActivePlans(Request $request)
    {
        $user = $request->user();

        // Get active purchases (completed and not expired)
        $activePurchases = $user->purchases()
            ->with(['plan', 'agent.user'])
            ->active()
            ->latest()
            ->get();

        $activePlans = $activePurchases->map(function ($purchase) {
            return [
                'purchase_id' => $purchase->id,
                'plan' => [
                    'id' => $purchase->plan->id,
                    'name' => $purchase->plan->name,
                    'type' => $purchase->plan->type,
                    'link' => $purchase->plan->link,
                    'price' => $purchase->plan->price,
                    'duration_days' => $purchase->plan->duration_days,
                    'features' => $purchase->plan->features,
                    'post_purchase_description' => $purchase->plan->post_purchase_description,
                    'banner' => $purchase->plan->banner ? url('storage/' . $purchase->plan->banner) : null,
                ],
                'purchase_details' => [
                    'amount' => $purchase->amount,
                    'activated_at' => $purchase->activated_at,
                    'expires_at' => $purchase->expires_at,
                    'remaining_days' => $purchase->remaining_days,
                    'is_active' => $purchase->isActive(),
                    'transaction_id' => $purchase->transaction_id,
                ],
                'agent' => $purchase->agent ? [
                    'name' => $purchase->agent->user->name,
                    'referral_code' => $purchase->agent->referral_code,
                ] : null,
            ];
        });

        // Get summary statistics
        $totalActivePlans = $activePlans->count();
        $totalSpent = $activePurchases->sum('amount');
        $nearestExpiry = $activePurchases->min('expires_at');

        return response()->json([
            'success' => true,
            'data' => [
                'active_plans' => $activePlans,
                'summary' => [
                    'total_active_plans' => $totalActivePlans,
                    'total_spent' => $totalSpent,
                    'nearest_expiry' => $nearestExpiry,
                    'has_active_plans' => $totalActivePlans > 0,
                ]
            ]
        ]);
    }
}
