<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Agent;
use App\Models\TelegramUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;

class TelegramAuthController extends Controller
{
    /**
     * Auto register/login user from Telegram for Next.js frontend
     */
    public function autoAuth(Request $request)
    {
        try {
            // Add CORS headers for Next.js frontend
            $this->addCorsHeaders();

            // Validate Telegram WebApp data
            $telegramData = $this->validateTelegramWebAppData($request->all());

            if (!$telegramData) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Telegram WebApp data',
                    'error_code' => 'INVALID_TELEGRAM_DATA'
                ], 400);
            }

            $agentId = $request->input('agent_id');
            $agent = Agent::find($agentId);

            if (!$agent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Agent not found',
                    'error_code' => 'AGENT_NOT_FOUND'
                ], 404);
            }

            // Find or create telegram user
            $telegramUser = TelegramUser::where('telegram_user_id', $telegramData['id'])
                ->whereHas('telegramBot', function($q) use ($agent) {
                    $q->where('agent_id', $agent->id);
                })
                ->first();

            if (!$telegramUser) {
                // Create telegram user record
                $telegramBot = $agent->telegramBot;
                if (!$telegramBot) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Telegram bot not configured for this agent'
                    ], 400);
                }

                $telegramUser = TelegramUser::create([
                    'telegram_bot_id' => $telegramBot->id,
                    'telegram_user_id' => $telegramData['id'],
                    'telegram_username' => $telegramData['username'] ?? null,
                    'first_name' => $telegramData['first_name'] ?? null,
                    'last_name' => $telegramData['last_name'] ?? null,
                    'last_interaction' => now(),
                ]);
            }

            // Check if user is already registered
            if ($telegramUser->is_registered && $telegramUser->user) {
                // Login existing user
                Auth::login($telegramUser->user);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Logged in successfully',
                    'user' => $telegramUser->user,
                    'agent' => $agent,
                    'redirect_url' => url('/dashboard')
                ]);
            }

            // Auto register new user
            $userData = $this->createUserFromTelegram($telegramData, $agent);
            $user = User::create($userData);

            // Link telegram user to website user
            $telegramUser->markAsRegistered($user);

            // Login the new user
            Auth::login($user);

            Log::info('Auto registration completed', [
                'user_id' => $user->id,
                'telegram_user_id' => $telegramData['id'],
                'agent_id' => $agent->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Registered and logged in successfully',
                'user' => $user,
                'agent' => $agent,
                'redirect_url' => url('/dashboard')
            ]);

        } catch (\Exception $e) {
            Log::error('Telegram auto auth error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Authentication failed'
            ], 500);
        }
    }

    /**
     * Validate Telegram WebApp data
     */
    private function validateTelegramData(array $data): ?array
    {
        // For now, we'll do basic validation
        // In production, you should validate the hash using bot token
        
        if (!isset($data['id']) || !isset($data['first_name'])) {
            return null;
        }

        return [
            'id' => $data['id'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'] ?? null,
            'username' => $data['username'] ?? null,
            'photo_url' => $data['photo_url'] ?? null,
        ];
    }

    /**
     * Create user data from Telegram info
     */
    private function createUserFromTelegram(array $telegramData, Agent $agent): array
    {
        $firstName = $telegramData['first_name'];
        $lastName = $telegramData['last_name'] ?? '';
        $username = $telegramData['username'] ?? null;
        
        // Generate email if username exists, otherwise use telegram ID
        $email = $username 
            ? $username . '@telegram.local'
            : 'user_' . $telegramData['id'] . '@telegram.local';

        // Generate a secure random password
        $password = Str::random(16);

        return [
            'name' => trim($firstName . ' ' . $lastName),
            'email' => $email,
            'password' => Hash::make($password),
            'email_verified_at' => now(), // Auto verify telegram users
            'referred_by_agent_id' => $agent->id,
            'telegram_id' => $telegramData['id'],
            'telegram_username' => $username,
            'profile_photo_url' => $telegramData['photo_url'] ?? null,
        ];
    }

    /**
     * Get current auth status
     */
    public function status(Request $request)
    {
        if (Auth::check()) {
            $user = Auth::user();
            $agent = null;
            
            if ($user->referred_by_agent_id) {
                $agent = Agent::find($user->referred_by_agent_id);
            }

            return response()->json([
                'authenticated' => true,
                'user' => $user,
                'agent' => $agent
            ]);
        }

        return response()->json([
            'authenticated' => false
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        Auth::logout();
        
        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }
}
