<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Agent;
use App\Models\TelegramUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class NextJsAuthController extends Controller
{
    // CORS headers are handled by middleware

    /**
     * Test endpoint to verify CORS and API connectivity
     */
    public function test(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'API is working perfectly!',
            'cors' => 'Open CORS enabled',
            'timestamp' => now(),
            'request_origin' => $request->header('Origin'),
            'user_agent' => $request->header('User-Agent')
        ]);
    }

    /**
     * Auto register/login user from Telegram WebApp - unified endpoint
     */
    public function telegramAuth(Request $request)
    {
        try {
            Log::info('Telegram auth request received', $request->all());

            // Validate required fields - can come from URL params or body
            $telegramId = $request->input('telegram_id') ?? $request->input('id');
            $firstName = $request->input('first_name');
            $lastName = $request->input('last_name');
            $username = $request->input('username');
            $photoUrl = $request->input('photo_url');
            $agentId = $request->input('agent_id');

            if (!$telegramId || !$firstName || !$agentId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing required parameters: telegram_id, first_name, agent_id',
                    'error_code' => 'MISSING_PARAMETERS'
                ], 400);
            }

            $agent = Agent::find($agentId);
            if (!$agent) {
                return response()->json([
                    'success' => false,
                    'message' => 'Agent not found',
                    'error_code' => 'AGENT_NOT_FOUND'
                ], 404);
            }

            // Find telegram bot for this agent
            $telegramBot = $agent->telegramBot;
            if (!$telegramBot) {
                return response()->json([
                    'success' => false,
                    'message' => 'Telegram bot not configured for this agent',
                    'error_code' => 'BOT_NOT_CONFIGURED'
                ], 400);
            }

            // First check if telegram_user record exists for this bot and telegram_id
            $telegramUser = TelegramUser::where('telegram_user_id', $telegramId)
                ->where('telegram_bot_id', $telegramBot->id)
                ->first();

            // Check if user already exists in our system (by telegram_id)
            $existingUser = User::where('telegram_id', $telegramId)->first();

            if ($existingUser) {
                // User exists - just login and return token
                $token = $existingUser->createToken('nextjs-auth')->plainTextToken;

                // Update user info if needed
                $existingUser->update([
                    'telegram_username' => $username,
                    'profile_photo_url' => $photoUrl,
                ]);

                // Update or create telegram_user record
                $telegramUser = $this->updateOrCreateTelegramUser(
                    $telegramUser, $telegramBot, $telegramId, $username,
                    $firstName, $lastName, $existingUser->id
                );

                Log::info('User logged in', [
                    'user_id' => $existingUser->id,
                    'telegram_id' => $telegramId,
                    'agent_id' => $agent->id
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'User logged in successfully',
                    'data' => [
                        'user' => [
                            'id' => $existingUser->id,
                            'name' => $existingUser->name,
                            'email' => $existingUser->email,
                            'telegram_id' => $existingUser->telegram_id,
                            'telegram_username' => $existingUser->telegram_username,
                            'profile_photo_url' => $existingUser->profile_photo_url,
                        ],
                        'agent' => [
                            'id' => $agent->id,
                            'name' => $agent->user->name,
                            'referral_code' => $agent->referral_code,
                        ],
                        'token' => $token,
                        'is_new_user' => false
                    ]
                ]);
            }

            // User doesn't exist - create new user
            $userData = [
                'name' => trim($firstName . ' ' . ($lastName ?? '')),
                'email' => $username ? $username . '@telegram.local' : 'user_' . $telegramId . '@telegram.local',
                'password' => Hash::make(Str::random(16)),
                'email_verified_at' => now(),
                'telegram_id' => $telegramId,
                'telegram_username' => $username,
                'profile_photo_url' => $photoUrl,
                'referred_by_agent_id' => $agent->id,
            ];

            $user = User::create($userData);

            // Update or create telegram user record
            $telegramUser = $this->updateOrCreateTelegramUser(
                $telegramUser, $telegramBot, $telegramId, $username,
                $firstName, $lastName, $user->id
            );

            // Generate token
            $token = $user->createToken('nextjs-auth')->plainTextToken;

            Log::info('User registered', [
                'user_id' => $user->id,
                'telegram_id' => $telegramId,
                'agent_id' => $agent->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'User registered and logged in successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'telegram_id' => $user->telegram_id,
                        'telegram_username' => $user->telegram_username,
                        'profile_photo_url' => $user->profile_photo_url,
                    ],
                    'agent' => [
                        'id' => $agent->id,
                        'name' => $agent->user->name,
                        'referral_code' => $agent->referral_code,
                    ],
                    'token' => $token,
                    'is_new_user' => true
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Telegram auth error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Authentication failed: ' . $e->getMessage(),
                'error_code' => 'AUTH_ERROR'
            ], 500);
        }
    }

    /**
     * Get user profile
     */
    public function profile(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized',
                'error_code' => 'UNAUTHORIZED'
            ], 401);
        }

        $agent = null;
        if ($user->referred_by_agent_id) {
            $agent = Agent::find($user->referred_by_agent_id);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'telegram_id' => $user->telegram_id,
                    'telegram_username' => $user->telegram_username,
                    'profile_photo_url' => $user->profile_photo_url,
                ],
                'agent' => $agent ? [
                    'id' => $agent->id,
                    'name' => $agent->user->name,
                    'referral_code' => $agent->referral_code,
                ] : null
            ]
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $user = $request->user();
        
        if ($user) {
            $user->currentAccessToken()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Update or create telegram user record
     */
    private function updateOrCreateTelegramUser($telegramUser, $telegramBot, $telegramId, $username, $firstName, $lastName, $userId)
    {
        if ($telegramUser) {
            // Update existing telegram_user record
            $telegramUser->update([
                'telegram_username' => $username,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'last_interaction' => now(),
                'is_registered' => true,
                'user_id' => $userId,
            ]);
            return $telegramUser;
        } else {
            // Create new telegram user record
            return TelegramUser::create([
                'telegram_bot_id' => $telegramBot->id,
                'telegram_user_id' => $telegramId,
                'telegram_username' => $username,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'last_interaction' => now(),
                'is_registered' => true,
                'user_id' => $userId,
            ]);
        }
    }
}
