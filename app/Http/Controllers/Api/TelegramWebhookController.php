<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\TelegramBot;
use App\Services\TelegramBotService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TelegramWebhookController extends Controller
{
    /**
     * Handle incoming webhook from Telegram
     */
    public function webhook(Request $request, $botId)
    {
        try {
            // Find the telegram bot
            $bot = TelegramBot::where('id', $botId)
                ->where('is_active', true)
                ->first();

            if (!$bot) {
                Log::warning('Webhook received for unknown or inactive bot', [
                    'bot_id' => $botId,
                    'request_data' => $request->all()
                ]);
                return response()->json(['error' => 'Bot not found'], 404);
            }

            // Get the update data
            $update = $request->all();

            // Log the incoming update
            Log::info('Telegram webhook received', [
                'bot_id' => $botId,
                'agent_id' => $bot->agent_id,
                'update_id' => $update['update_id'] ?? null,
                'message_type' => $this->getMessageType($update)
            ]);

            // Process the update using the service
            $telegramService = new TelegramBotService($bot);
            $result = $telegramService->processUpdate($update);

            if ($result) {
                return response()->json(['status' => 'ok']);
            } else {
                return response()->json(['status' => 'error'], 500);
            }

        } catch (\Exception $e) {
            Log::error('Telegram webhook error', [
                'bot_id' => $botId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json(['error' => 'Internal server error'], 500);
        }
    }

    /**
     * Test webhook endpoint
     */
    public function test(Request $request, $botId)
    {
        try {
            $bot = TelegramBot::find($botId);

            if (!$bot) {
                return response()->json(['error' => 'Bot not found'], 404);
            }

            $telegramService = new TelegramBotService($bot);
            $botInfo = $telegramService->getBotInfo();

            return response()->json([
                'success' => true,
                'bot_info' => $botInfo,
                'webhook_url' => $bot->getWebhookUrl(),
                'bot_status' => [
                    'is_active' => $bot->is_active,
                    'has_webhook' => $bot->hasWebhook(),
                    'webhook_set_at' => $bot->webhook_set_at,
                ],
                'environment' => app()->environment(),
                'webhook_skipped' => app()->environment('local')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simulate webhook for testing in local environment
     */
    public function simulate(Request $request, $botId)
    {
        if (!app()->environment('local')) {
            return response()->json(['error' => 'This endpoint is only available in local environment'], 403);
        }

        try {
            $bot = TelegramBot::find($botId);

            if (!$bot) {
                return response()->json(['error' => 'Bot not found'], 404);
            }

            // Simulate a /start command from a test user
            $simulatedUpdate = [
                'update_id' => rand(100000, 999999),
                'message' => [
                    'message_id' => rand(1000, 9999),
                    'from' => [
                        'id' => rand(100000000, 999999999),
                        'is_bot' => false,
                        'first_name' => 'Test',
                        'last_name' => 'User',
                        'username' => 'testuser',
                        'language_code' => 'fa'
                    ],
                    'chat' => [
                        'id' => rand(100000000, 999999999),
                        'first_name' => 'Test',
                        'last_name' => 'User',
                        'username' => 'testuser',
                        'type' => 'private'
                    ],
                    'date' => time(),
                    'text' => '/start'
                ]
            ];

            $telegramService = new TelegramBotService($bot);
            $result = $telegramService->processUpdate($simulatedUpdate);

            return response()->json([
                'success' => true,
                'message' => 'Webhook simulation completed',
                'simulated_update' => $simulatedUpdate,
                'processing_result' => $result
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Set webhook for a bot
     */
    public function setWebhook(Request $request, $botId)
    {
        try {
            $bot = TelegramBot::find($botId);
            
            if (!$bot) {
                return response()->json(['error' => 'Bot not found'], 404);
            }

            $telegramService = new TelegramBotService($bot);
            $result = $telegramService->setWebhook();

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Webhook set successfully',
                    'webhook_url' => $bot->getWebhookUrl()
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to set webhook'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove webhook for a bot
     */
    public function removeWebhook(Request $request, $botId)
    {
        try {
            $bot = TelegramBot::find($botId);
            
            if (!$bot) {
                return response()->json(['error' => 'Bot not found'], 404);
            }

            $telegramService = new TelegramBotService($bot);
            $result = $telegramService->removeWebhook();

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Webhook removed successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to remove webhook'
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get message type from update
     */
    private function getMessageType(array $update): string
    {
        if (isset($update['message'])) {
            if (isset($update['message']['text'])) {
                return 'text';
            } elseif (isset($update['message']['photo'])) {
                return 'photo';
            } elseif (isset($update['message']['document'])) {
                return 'document';
            } else {
                return 'other_message';
            }
        } elseif (isset($update['callback_query'])) {
            return 'callback_query';
        } elseif (isset($update['inline_query'])) {
            return 'inline_query';
        } else {
            return 'unknown';
        }
    }
}
