<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\PlanResource;
use App\Models\Plan;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    public function index(Request $request)
    {
        $plans = Plan::active()->ordered()->get();

        return response()->json([
            'success' => true,
            'data' => [
                'plans' => PlanResource::collection($plans)
            ],
            'meta' => [
                'total' => $plans->count(),
                'active_plans' => $plans->where('status', 'active')->count(),
            ]
        ]);
    }

    public function show(Request $request, Plan $plan)
    {
        if (!$plan->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'پلن مورد نظر در دسترس نیست'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'plan' => new PlanResource($plan)
            ]
        ]);
    }

    /**
     * Get all plans (for admin)
     */
    public function all(Request $request)
    {
        $query = Plan::query();

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('type')) {
            $query->where('type', 'like', '%' . $request->type . '%');
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $plans = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'plans' => PlanResource::collection($plans->items())
            ],
            'meta' => [
                'current_page' => $plans->currentPage(),
                'last_page' => $plans->lastPage(),
                'per_page' => $plans->perPage(),
                'total' => $plans->total(),
                'from' => $plans->firstItem(),
                'to' => $plans->lastItem(),
            ],
            'links' => [
                'first' => $plans->url(1),
                'last' => $plans->url($plans->lastPage()),
                'prev' => $plans->previousPageUrl(),
                'next' => $plans->nextPageUrl(),
            ]
        ]);
    }

    /**
     * Get plan statistics
     */
    public function statistics(Plan $plan)
    {
        $stats = [
            'total_purchases' => $plan->purchases()->count(),
            'completed_purchases' => $plan->purchases()->where('status', 'completed')->count(),
            'pending_purchases' => $plan->purchases()->where('status', 'pending')->count(),
            'failed_purchases' => $plan->purchases()->where('status', 'failed')->count(),
            'active_subscriptions' => $plan->purchases()->active()->count(),
            'expired_subscriptions' => $plan->purchases()->expired()->count(),
            'total_revenue' => $plan->purchases()->where('status', 'completed')->sum('amount'),
            'total_commission' => $plan->purchases()->where('status', 'completed')->sum('commission_amount'),
            'monthly_stats' => $this->getMonthlyStats($plan),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'plan' => [
                    'id' => $plan->id,
                    'name' => $plan->name,
                ],
                'statistics' => $stats
            ]
        ]);
    }

    /**
     * Get monthly statistics for a plan
     */
    private function getMonthlyStats(Plan $plan)
    {
        $monthlyStats = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $startOfMonth = $date->copy()->startOfMonth();
            $endOfMonth = $date->copy()->endOfMonth();

            $purchases = $plan->purchases()
                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
                ->where('status', 'completed');

            $monthlyStats[] = [
                'month' => $date->format('Y-m'),
                'month_name' => $date->format('F Y'),
                'month_persian' => $this->getPersianMonth($date),
                'purchases_count' => $purchases->count(),
                'revenue' => $purchases->sum('amount'),
                'commission' => $purchases->sum('commission_amount'),
            ];
        }

        return $monthlyStats;
    }

    /**
     * Get Persian month name
     */
    private function getPersianMonth($date)
    {
        $persianMonths = [
            1 => 'فروردین', 2 => 'اردیبهشت', 3 => 'خرداد',
            4 => 'تیر', 5 => 'مرداد', 6 => 'شهریور',
            7 => 'مهر', 8 => 'آبان', 9 => 'آذر',
            10 => 'دی', 11 => 'بهمن', 12 => 'اسفند'
        ];

        return $persianMonths[$date->month] . ' ' . $date->year;
    }
}
