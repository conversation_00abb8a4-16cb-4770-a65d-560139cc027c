<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\WithdrawalRequest;
use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WithdrawalController extends Controller
{
    public function index(Request $request)
    {
        $query = WithdrawalRequest::with(['agent.user']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $withdrawals = $query->latest()->paginate(15);

        $stats = [
            'pending_amount' => WithdrawalRequest::pending()->sum('amount'),
            'pending_count' => WithdrawalRequest::pending()->count(),
            'completed_amount' => WithdrawalRequest::completed()->sum('amount'),
            'completed_count' => WithdrawalRequest::completed()->count(),
            'crypto_pending' => WithdrawalRequest::pending()->crypto()->sum('amount'),
            'bank_pending' => WithdrawalRequest::pending()->bank()->sum('amount'),
        ];

        return view('admin.withdrawals.index', compact('withdrawals', 'stats'));
    }

    public function show(WithdrawalRequest $withdrawal)
    {
        $withdrawal->load(['agent.user']);
        return view('admin.withdrawals.show', compact('withdrawal'));
    }

    public function approve(Request $request, WithdrawalRequest $withdrawal)
    {
        if (!$withdrawal->isPending()) {
            return redirect()->back()
                ->with('error', 'این درخواست قابل تایید نیست.');
        }

        $request->validate([
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $withdrawal->update([
            'status' => WithdrawalRequest::STATUS_APPROVED,
            'admin_notes' => $request->admin_notes,
        ]);

        return redirect()->route('admin.withdrawals.index')
            ->with('success', 'درخواست تسویه حساب تایید شد.');
    }

    public function reject(Request $request, WithdrawalRequest $withdrawal)
    {
        if (!$withdrawal->isPending()) {
            return redirect()->back()
                ->with('error', 'این درخواست قابل رد نیست.');
        }

        $request->validate([
            'admin_notes' => 'required|string|max:500',
        ]);

        $withdrawal->update([
            'status' => WithdrawalRequest::STATUS_REJECTED,
            'admin_notes' => $request->admin_notes,
        ]);

        return redirect()->route('admin.withdrawals.index')
            ->with('success', 'درخواست تسویه حساب رد شد.');
    }

    public function markAsProcessing(WithdrawalRequest $withdrawal)
    {
        if (!$withdrawal->isApproved()) {
            return redirect()->back()
                ->with('error', 'فقط درخواست‌های تایید شده قابل پردازش هستند.');
        }

        $withdrawal->update([
            'status' => WithdrawalRequest::STATUS_PROCESSING,
        ]);

        return redirect()->route('admin.withdrawals.index')
            ->with('success', 'وضعیت درخواست به "در حال پردازش" تغییر کرد.');
    }

    public function complete(Request $request, WithdrawalRequest $withdrawal)
    {
        if (!in_array($withdrawal->status, [WithdrawalRequest::STATUS_APPROVED, WithdrawalRequest::STATUS_PROCESSING])) {
            return redirect()->back()
                ->with('error', 'این درخواست قابل تکمیل نیست.');
        }

        $request->validate([
            'transaction_hash' => 'nullable|string|max:255',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        // Check if agent has enough balance
        if ($withdrawal->agent->wallet_balance < $withdrawal->amount) {
            return redirect()->back()
                ->with('error', 'موجودی نماینده کافی نیست.');
        }

        try {
            DB::beginTransaction();

            // Update withdrawal request
            $withdrawal->update([
                'status' => WithdrawalRequest::STATUS_COMPLETED,
                'transaction_hash' => $request->transaction_hash,
                'admin_notes' => $request->admin_notes,
                'processed_at' => now(),
            ]);

            // Deduct amount from agent's wallet
            $withdrawal->agent->decrement('wallet_balance', $withdrawal->amount);

            // Create transaction record
            Transaction::create([
                'user_id' => $withdrawal->agent->user_id,
                'agent_id' => $withdrawal->agent_id,
                'type' => Transaction::TYPE_WITHDRAWAL,
                'amount' => $withdrawal->amount,
                'status' => Transaction::STATUS_COMPLETED,
                'description' => 'برداشت از کیف پول - ' . $withdrawal->method_in_persian,
                'reference_id' => $withdrawal->id,
                'metadata' => [
                    'method' => $withdrawal->method,
                    'crypto_type' => $withdrawal->crypto_wallet_type,
                    'crypto_address' => $withdrawal->crypto_wallet_address,
                    'crypto_network' => $withdrawal->crypto_network,
                    'transaction_hash' => $request->transaction_hash,
                    'bank_account' => $withdrawal->bank_account_number,
                ]
            ]);

            DB::commit();

            return redirect()->route('admin.withdrawals.index')
                ->with('success', 'درخواست تسویه حساب تکمیل شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                ->with('error', 'خطا در تکمیل درخواست. لطفاً دوباره تلاش کنید.');
        }
    }

    public function bulkApprove(Request $request)
    {
        $request->validate([
            'withdrawal_ids' => 'required|array',
            'withdrawal_ids.*' => 'exists:withdrawal_requests,id',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $withdrawals = WithdrawalRequest::whereIn('id', $request->withdrawal_ids)
            ->where('status', WithdrawalRequest::STATUS_PENDING)
            ->get();

        $approvedCount = 0;
        foreach ($withdrawals as $withdrawal) {
            $withdrawal->update([
                'status' => WithdrawalRequest::STATUS_APPROVED,
                'admin_notes' => $request->admin_notes,
            ]);
            $approvedCount++;
        }

        return redirect()->route('admin.withdrawals.index')
            ->with('success', "تعداد {$approvedCount} درخواست تایید شد.");
    }

    public function export(Request $request)
    {
        $query = WithdrawalRequest::with(['agent.user']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $withdrawals = $query->get();

        $filename = 'withdrawal_requests_' . now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($withdrawals) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Headers
            fputcsv($file, [
                'شناسه',
                'نماینده',
                'مبلغ',
                'روش پرداخت',
                'وضعیت',
                'تاریخ درخواست',
                'تاریخ پردازش',
                'هش تراکنش',
            ]);

            foreach ($withdrawals as $withdrawal) {
                fputcsv($file, [
                    $withdrawal->id,
                    $withdrawal->agent->user->name,
                    $withdrawal->amount,
                    $withdrawal->method_in_persian,
                    $withdrawal->status_in_persian,
                    $withdrawal->created_at->format('Y/m/d H:i'),
                    $withdrawal->processed_at ? $withdrawal->processed_at->format('Y/m/d H:i') : '-',
                    $withdrawal->transaction_hash ?: '-',
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
