<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Agent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class AgentController extends Controller
{
    public function index(Request $request)
    {
        $query = Agent::with('user');

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('referral_code', 'like', "%{$search}%");
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $agents = $query->latest()->paginate(15);

        return view('admin.agents.index', compact('agents'));
    }

    public function create()
    {
        return view('admin.agents.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'bank_account_number' => 'nullable|string|max:50',
            'bank_account_name' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
        ]);

        // Create user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'password' => Hash::make($request->password),
            'role' => User::ROLE_AGENT,
            'is_active' => true,
        ]);

        // Create agent profile
        Agent::create([
            'user_id' => $user->id,
            'commission_rate' => $request->commission_rate,
            'bank_account_number' => $request->bank_account_number,
            'bank_account_name' => $request->bank_account_name,
            'bank_name' => $request->bank_name,
            'is_active' => true,
        ]);

        return redirect()->route('admin.agents.index')
            ->with('success', 'نماینده با موفقیت ایجاد شد.');
    }

    public function show(Agent $agent)
    {
        $agent->load(['user', 'purchases.plan', 'commissions']);
        
        $stats = [
            'total_sales' => $agent->total_sales,
            'total_commission' => $agent->total_commission,
            'wallet_balance' => $agent->wallet_balance,
            'total_customers' => $agent->purchases()->distinct('user_id')->count(),
            'this_month_sales' => $agent->getMonthlySales(now()->year, now()->month),
        ];

        return view('admin.agents.show', compact('agent', 'stats'));
    }

    public function edit(Agent $agent)
    {
        $agent->load('user');
        return view('admin.agents.edit', compact('agent'));
    }

    public function update(Request $request, Agent $agent)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($agent->user_id)],
            'phone' => 'nullable|string|max:20',
            'commission_rate' => 'required|numeric|min:0|max:100',
            'bank_account_number' => 'nullable|string|max:50',
            'bank_account_name' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
            'is_active' => 'boolean',
        ]);

        // Update user
        $agent->user->update([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Update agent
        $agent->update([
            'commission_rate' => $request->commission_rate,
            'bank_account_number' => $request->bank_account_number,
            'bank_account_name' => $request->bank_account_name,
            'bank_name' => $request->bank_name,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('admin.agents.index')
            ->with('success', 'اطلاعات نماینده با موفقیت به‌روزرسانی شد.');
    }

    public function destroy(Agent $agent)
    {
        $agent->user->delete(); // This will cascade delete the agent
        
        return redirect()->route('admin.agents.index')
            ->with('success', 'نماینده با موفقیت حذف شد.');
    }
}
