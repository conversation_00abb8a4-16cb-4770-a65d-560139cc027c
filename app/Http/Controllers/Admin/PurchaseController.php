<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Purchase;
use App\Models\User;
use App\Models\Plan;
use App\Models\Agent;
use App\Models\Commission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PurchaseController extends Controller
{
    /**
     * Display a listing of purchases
     */
    public function index(Request $request)
    {
        $query = Purchase::with(['user', 'plan', 'agent.user']);

        // Search filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('plan', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhere('transaction_id', 'like', "%{$search}%");
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('plan_id')) {
            $query->where('plan_id', $request->plan_id);
        }

        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $purchases = $query->latest()->paginate(15);

        // Get filter options
        $plans = Plan::active()->orderBy('name')->get();
        $agents = Agent::with('user')->get();

        $stats = [
            'total_purchases' => Purchase::count(),
            'completed_purchases' => Purchase::where('status', Purchase::STATUS_COMPLETED)->count(),
            'pending_purchases' => Purchase::where('status', Purchase::STATUS_PENDING)->count(),
            'total_revenue' => Purchase::where('status', Purchase::STATUS_COMPLETED)->sum('amount'),
            'active_subscriptions' => Purchase::active()->count(),
        ];

        return view('admin.purchases.index', compact('purchases', 'plans', 'agents', 'stats'));
    }

    /**
     * Show the form for creating a new purchase (manual activation)
     */
    public function create(Request $request)
    {
        $users = User::where('role', User::ROLE_USER)->orderBy('name')->get();
        $plans = Plan::active()->orderBy('name')->get();
        $agents = Agent::with('user')->where('is_active', true)->get();

        // Pre-select user if provided
        $selectedUser = null;
        if ($request->filled('user_id')) {
            $selectedUser = User::find($request->user_id);
        }

        return view('admin.purchases.create', compact('users', 'plans', 'agents', 'selectedUser'));
    }

    /**
     * Store a newly created purchase (manual activation)
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'plan_id' => 'required|exists:plans,id',
            'agent_id' => 'nullable|exists:agents,id',
            'amount' => 'nullable|numeric|min:0',
            'duration_days' => 'nullable|integer|min:1',
            'notes' => 'nullable|string|max:1000',
        ]);

        $user = User::findOrFail($request->user_id);
        $plan = Plan::findOrFail($request->plan_id);
        $agent = $request->agent_id ? Agent::findOrFail($request->agent_id) : null;

        // Calculate amount (use custom amount or plan price)
        $amount = $request->filled('amount') ? $request->amount : $plan->price;
        
        // Calculate commission if agent exists
        $commissionAmount = 0;
        if ($agent && $amount > 0) {
            $commissionAmount = ($amount * $agent->commission_rate) / 100;
        }

        // Calculate expiry date
        $durationDays = $request->filled('duration_days') ? $request->duration_days : $plan->duration_days;
        $expiresAt = now()->addDays($durationDays);

        DB::beginTransaction();
        try {
            // Create purchase
            $purchase = Purchase::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'agent_id' => $agent?->id,
                'amount' => $amount,
                'commission_amount' => $commissionAmount,
                'status' => Purchase::STATUS_COMPLETED,
                'payment_method' => 'manual_admin',
                'transaction_id' => 'ADMIN_' . time() . '_' . $user->id,
                'expires_at' => $expiresAt,
                'activated_at' => now(),
                'notes' => $request->notes,
            ]);

            // Create commission record if agent exists
            if ($agent && $commissionAmount > 0) {
                Commission::create([
                    'agent_id' => $agent->id,
                    'purchase_id' => $purchase->id,
                    'amount' => $commissionAmount,
                    'status' => Commission::STATUS_PENDING,
                ]);
            }

            DB::commit();

            return redirect()->route('admin.purchases.index')
                ->with('success', "پلن {$plan->name} با موفقیت برای {$user->name} فعال شد.");

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withErrors(['error' => 'خطا در فعال‌سازی پلن: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified purchase
     */
    public function show(Purchase $purchase)
    {
        $purchase->load(['user', 'plan', 'agent.user', 'commission']);
        return view('admin.purchases.show', compact('purchase'));
    }

    /**
     * Show the form for editing the specified purchase
     */
    public function edit(Purchase $purchase)
    {
        $plans = Plan::active()->orderBy('name')->get();
        $agents = Agent::with('user')->where('is_active', true)->get();
        
        return view('admin.purchases.edit', compact('purchase', 'plans', 'agents'));
    }

    /**
     * Update the specified purchase
     */
    public function update(Request $request, Purchase $purchase)
    {
        $request->validate([
            'status' => 'required|in:pending,completed,failed,refunded',
            'expires_at' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:1000',
        ]);

        $purchase->update([
            'status' => $request->status,
            'expires_at' => $request->expires_at,
            'notes' => $request->notes,
        ]);

        return redirect()->route('admin.purchases.show', $purchase)
            ->with('success', 'خرید با موفقیت به‌روزرسانی شد.');
    }

    /**
     * Extend purchase expiry date
     */
    public function extend(Request $request, Purchase $purchase)
    {
        $request->validate([
            'extend_days' => 'required|integer|min:1|max:365',
        ]);

        $newExpiryDate = $purchase->expires_at->addDays($request->extend_days);
        
        $purchase->update([
            'expires_at' => $newExpiryDate,
        ]);

        return back()->with('success', "تاریخ انقضا {$request->extend_days} روز تمدید شد.");
    }

    /**
     * Activate/Deactivate purchase
     */
    public function toggleStatus(Purchase $purchase)
    {
        $newStatus = $purchase->status === Purchase::STATUS_COMPLETED 
            ? Purchase::STATUS_PENDING 
            : Purchase::STATUS_COMPLETED;

        $purchase->update([
            'status' => $newStatus,
            'activated_at' => $newStatus === Purchase::STATUS_COMPLETED ? now() : null,
        ]);

        $statusText = $newStatus === Purchase::STATUS_COMPLETED ? 'فعال' : 'غیرفعال';
        
        return back()->with('success', "وضعیت خرید به {$statusText} تغییر یافت.");
    }

    /**
     * Remove the specified purchase
     */
    public function destroy(Purchase $purchase)
    {
        // Delete related commission if exists
        if ($purchase->commission) {
            $purchase->commission->delete();
        }

        $purchase->delete();

        return redirect()->route('admin.purchases.index')
            ->with('success', 'خرید با موفقیت حذف شد.');
    }
}
