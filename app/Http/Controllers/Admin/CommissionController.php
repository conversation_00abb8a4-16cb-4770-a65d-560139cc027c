<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Commission;
use Illuminate\Http\Request;

class CommissionController extends Controller
{
    public function index(Request $request)
    {
        $query = Commission::with(['agent.user', 'purchase.plan']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('agent_id')) {
            $query->where('agent_id', $request->agent_id);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $commissions = $query->latest()->paginate(15);

        $stats = [
            'pending_amount' => Commission::pending()->sum('amount'),
            'paid_amount' => Commission::paid()->sum('amount'),
            'total_amount' => Commission::sum('amount'),
        ];

        return view('admin.commissions.index', compact('commissions', 'stats'));
    }

    public function show(Commission $commission)
    {
        $commission->load(['agent.user', 'purchase.plan', 'purchase.user']);
        return view('admin.commissions.show', compact('commission'));
    }

    public function pay(Request $request, Commission $commission)
    {
        if (!$commission->isPending()) {
            return redirect()->back()
                ->with('error', 'این کمیسیون قابل پرداخت نیست.');
        }

        $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        $commission->markAsPaid($request->notes);

        return redirect()->route('admin.commissions.index')
            ->with('success', 'کمیسیون با موفقیت پرداخت شد.');
    }

    public function cancel(Request $request, Commission $commission)
    {
        if (!$commission->isPending()) {
            return redirect()->back()
                ->with('error', 'این کمیسیون قابل لغو نیست.');
        }

        $request->validate([
            'notes' => 'required|string|max:500',
        ]);

        $commission->cancel($request->notes);

        return redirect()->route('admin.commissions.index')
            ->with('success', 'کمیسیون لغو شد.');
    }

    public function bulkPay(Request $request)
    {
        $request->validate([
            'commission_ids' => 'required|array',
            'commission_ids.*' => 'exists:commissions,id',
            'notes' => 'nullable|string|max:500',
        ]);

        $commissions = Commission::whereIn('id', $request->commission_ids)
            ->where('status', 'pending')
            ->get();

        $totalAmount = 0;
        foreach ($commissions as $commission) {
            $commission->markAsPaid($request->notes);
            $totalAmount += $commission->amount;
        }

        return redirect()->route('admin.commissions.index')
            ->with('success', "تعداد {$commissions->count()} کمیسیون به مبلغ " . number_format($totalAmount) . " تومان پرداخت شد.");
    }
}
