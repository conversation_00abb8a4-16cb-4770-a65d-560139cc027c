<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    public function index()
    {
        $plans = Plan::orderBy('sort_order')->paginate(15);
        return view('admin.plans.index', compact('plans'));
    }

    public function create()
    {
        return view('admin.plans.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'post_purchase_description' => 'nullable|string',
            'link' => 'nullable|url',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $bannerPath = null;
        if ($request->hasFile('banner')) {
            $bannerPath = $request->file('banner')->store('plan-banners', 'public');
        }

        Plan::create([
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'post_purchase_description' => $request->post_purchase_description,
            'link' => $request->link,
            'banner' => $bannerPath,
            'price' => $request->price,
            'duration_days' => $request->duration_days,
            'features' => $request->features ? array_filter($request->features) : null,
            'status' => $request->status,
            'sort_order' => $request->sort_order ?? 0,
        ]);

        return redirect()->route('admin.plans.index')
            ->with('success', 'پلن با موفقیت ایجاد شد.');
    }

    public function show(Plan $plan)
    {
        $plan->load('purchases.user');
        
        $stats = [
            'total_purchases' => $plan->purchases()->count(),
            'completed_purchases' => $plan->purchases()->where('status', 'completed')->count(),
            'total_revenue' => $plan->purchases()->where('status', 'completed')->sum('amount'),
            'active_subscriptions' => $plan->purchases()->active()->count(),
        ];

        return view('admin.plans.show', compact('plan', 'stats'));
    }

    public function edit(Plan $plan)
    {
        return view('admin.plans.edit', compact('plan'));
    }

    public function update(Request $request, Plan $plan)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'post_purchase_description' => 'nullable|string',
            'link' => 'nullable|url',
            'banner' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'price' => 'required|numeric|min:0',
            'duration_days' => 'required|integer|min:1',
            'features' => 'nullable|array',
            'features.*' => 'string',
            'status' => 'required|in:active,inactive',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        $updateData = [
            'name' => $request->name,
            'type' => $request->type,
            'description' => $request->description,
            'post_purchase_description' => $request->post_purchase_description,
            'link' => $request->link,
            'price' => $request->price,
            'duration_days' => $request->duration_days,
            'features' => $request->features ? array_filter($request->features) : null,
            'status' => $request->status,
            'sort_order' => $request->sort_order ?? 0,
        ];

        // Handle banner upload
        if ($request->hasFile('banner')) {
            // Delete old banner if exists
            if ($plan->banner && \Storage::disk('public')->exists($plan->banner)) {
                \Storage::disk('public')->delete($plan->banner);
            }
            $updateData['banner'] = $request->file('banner')->store('plan-banners', 'public');
        }

        $plan->update($updateData);

        return redirect()->route('admin.plans.index')
            ->with('success', 'پلن با موفقیت به‌روزرسانی شد.');
    }

    public function destroy(Plan $plan)
    {
        if ($plan->purchases()->exists()) {
            return redirect()->route('admin.plans.index')
                ->with('error', 'این پلن دارای خرید است و قابل حذف نیست.');
        }

        // Delete banner file if exists
        if ($plan->banner && \Storage::disk('public')->exists($plan->banner)) {
            \Storage::disk('public')->delete($plan->banner);
        }

        $plan->delete();

        return redirect()->route('admin.plans.index')
            ->with('success', 'پلن با موفقیت حذف شد.');
    }
}
