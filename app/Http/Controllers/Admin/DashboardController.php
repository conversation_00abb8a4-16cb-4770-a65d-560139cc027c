<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Agent;
use App\Models\Plan;
use App\Models\Purchase;
use App\Models\Commission;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_agents' => Agent::count(),
            'active_agents' => Agent::where('is_active', true)->count(),
            'total_plans' => Plan::count(),
            'active_plans' => Plan::where('status', 'active')->count(),
            'total_purchases' => Purchase::count(),
            'completed_purchases' => Purchase::where('status', 'completed')->count(),
            'total_sales' => Purchase::where('status', 'completed')->sum('amount'),
            'pending_commissions' => Commission::where('status', 'pending')->sum('amount'),
            'paid_commissions' => Commission::where('status', 'paid')->sum('amount'),
        ];

        // Recent purchases
        $recent_purchases = Purchase::with(['user', 'plan', 'agent.user'])
            ->latest()
            ->limit(10)
            ->get();

        // Top agents by sales
        $top_agents = Agent::with('user')
            ->orderBy('total_sales', 'desc')
            ->limit(5)
            ->get();

        // Monthly sales chart data
        $monthly_sales = Purchase::where('status', 'completed')
            ->whereYear('created_at', now()->year)
            ->selectRaw('MONTH(created_at) as month, SUM(amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->pluck('total', 'month')
            ->toArray();

        return view('admin.dashboard', compact('stats', 'recent_purchases', 'top_agents', 'monthly_sales'));
    }
}
