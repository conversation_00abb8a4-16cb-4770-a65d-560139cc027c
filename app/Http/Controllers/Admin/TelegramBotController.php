<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agent;
use App\Models\TelegramBot;
use App\Services\TelegramBotService;
use Illuminate\Http\Request;

class TelegramBotController extends Controller
{
    // public function __construct()
    // {
    //     $this->middleware('admin');
    // }

    /**
     * Display a listing of telegram bots
     */
    public function index(Request $request)
    {
        $query = TelegramBot::with('agent.user');

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->whereHas('agent.user', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhere('bot_username', 'like', "%{$search}%");
        }

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('is_active', $request->status);
        }

        $bots = $query->latest()->paginate(20);
        
        // Get agents without telegram bots
        $agentsWithoutBots = Agent::whereDoesntHave('telegramBot')
            ->with('user')
            ->get();

        return view('admin.telegram-bots.index', compact('bots', 'agentsWithoutBots'));
    }

    /**
     * Show the form for creating a new telegram bot
     */
    public function create(Request $request)
    {
        $agentId = $request->get('agent_id');
        $agent = null;
        
        if ($agentId) {
            $agent = Agent::with('user')->find($agentId);
            if (!$agent || $agent->telegramBot) {
                return redirect()->route('admin.telegram-bots.index')
                    ->with('error', 'نماینده یافت نشد یا قبلاً ربات تلگرام دارد.');
            }
        }

        $agents = Agent::whereDoesntHave('telegramBot')->with('user')->get();
        
        return view('admin.telegram-bots.create', compact('agents', 'agent'));
    }

    /**
     * Store a newly created telegram bot
     */
    public function store(Request $request)
    {
        $request->validate([
            'agent_id' => 'required|exists:agents,id',
            'bot_token' => 'required|string',
            'bot_username' => 'required|string|regex:/^[a-zA-Z0-9_]+$/',
            'welcome_message' => 'nullable|string|max:4000',
        ]);

        // Check if agent already has a bot
        $agent = Agent::find($request->agent_id);
        if ($agent->telegramBot) {
            return back()->withErrors(['agent_id' => 'این نماینده قبلاً ربات تلگرام دارد.']);
        }

        try {
            // Test the bot token first
            $testService = new TelegramBotService();
            $testBot = new TelegramBot(['bot_token' => $request->bot_token]);
            $testService->setBot($testBot);
            $botInfo = $testService->getBotInfo();

            if (!$botInfo || !$botInfo['ok']) {
                return back()->withErrors(['bot_token' => 'توکن ربات نامعتبر است.']);
            }

            // Check if username matches
            $actualUsername = $botInfo['result']['username'];
            if (strtolower($actualUsername) !== strtolower($request->bot_username)) {
                return back()->withErrors([
                    'bot_username' => "نام کاربری ربات باید {$actualUsername} باشد."
                ]);
            }

            // Check if bot token is already used
            if (TelegramBot::where('bot_token', $request->bot_token)->exists()) {
                return back()->withErrors(['bot_token' => 'این توکن قبلاً استفاده شده است.']);
            }

            // Create telegram bot
            $telegramBot = TelegramBot::create([
                'agent_id' => $request->agent_id,
                'bot_token' => $request->bot_token,
                'bot_username' => $request->bot_username,
                'welcome_message' => $request->welcome_message,
                'is_active' => true,
            ]);

            // Set webhook
            $telegramService = new TelegramBotService($telegramBot);
            $webhookResult = $telegramService->setWebhook();

            if ($webhookResult) {
                return redirect()->route('admin.telegram-bots.index')
                    ->with('success', 'ربات تلگرام با موفقیت ایجاد شد.');
            } else {

                // Delete the bot if webhook setup failed
                $telegramBot->delete();
                dd($webhookResult);
                return back()->withErrors(['webhook' => 'خطا در تنظیم webhook ربات.']);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطا در ایجاد ربات: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the specified telegram bot
     */
    public function show(TelegramBot $telegramBot)
    {
        $telegramBot->load('agent.user', 'telegramUsers');
        
        $stats = [
            'total_users' => $telegramBot->getTotalUsersCount(),
            'active_users' => $telegramBot->getActiveUsersCount(),
            'registered_users' => $telegramBot->getRegisteredUsersCount(),
        ];

        return view('admin.telegram-bots.show', compact('telegramBot', 'stats'));
    }

    /**
     * Show the form for editing the specified telegram bot
     */
    public function edit(TelegramBot $telegramBot)
    {
        $telegramBot->load('agent.user');
        return view('admin.telegram-bots.edit', compact('telegramBot'));
    }

    /**
     * Update the specified telegram bot
     */
    public function update(Request $request, TelegramBot $telegramBot)
    {
        $request->validate([
            'bot_token' => 'required|string',
            'bot_username' => 'required|string|regex:/^[a-zA-Z0-9_]+$/',
            'welcome_message' => 'nullable|string|max:4000',
            'is_active' => 'boolean',
        ]);

        try {
            // Test the bot token if it's changed
            if ($request->bot_token !== $telegramBot->bot_token) {
                $testService = new TelegramBotService();
                $testBot = new TelegramBot(['bot_token' => $request->bot_token]);
                $testService->setBot($testBot);
                $botInfo = $testService->getBotInfo();

                if (!$botInfo || !$botInfo['ok']) {
                    return back()->withErrors(['bot_token' => 'توکن ربات نامعتبر است.']);
                }

                // Check if username matches
                $actualUsername = $botInfo['result']['username'];
                if (strtolower($actualUsername) !== strtolower($request->bot_username)) {
                    return back()->withErrors([
                        'bot_username' => "نام کاربری ربات باید {$actualUsername} باشد."
                    ]);
                }

                // Check if bot token is already used by another bot
                if (TelegramBot::where('bot_token', $request->bot_token)
                    ->where('id', '!=', $telegramBot->id)->exists()) {
                    return back()->withErrors(['bot_token' => 'این توکن قبلاً استفاده شده است.']);
                }
            }

            // Update telegram bot
            $telegramBot->update([
                'bot_token' => $request->bot_token,
                'bot_username' => $request->bot_username,
                'welcome_message' => $request->welcome_message,
                'is_active' => $request->has('is_active'),
            ]);

            // Update webhook if token changed
            if ($request->bot_token !== $telegramBot->getOriginal('bot_token')) {
                $telegramService = new TelegramBotService($telegramBot);
                $telegramService->setWebhook();
            }

            return redirect()->route('admin.telegram-bots.index')
                ->with('success', 'ربات تلگرام با موفقیت به‌روزرسانی شد.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطا در به‌روزرسانی ربات: ' . $e->getMessage()]);
        }
    }

    /**
     * Remove the specified telegram bot
     */
    public function destroy(TelegramBot $telegramBot)
    {
        try {
            // Remove webhook first
            $telegramService = new TelegramBotService($telegramBot);
            $telegramService->removeWebhook();

            // Delete the bot
            $telegramBot->delete();

            return redirect()->route('admin.telegram-bots.index')
                ->with('success', 'ربات تلگرام با موفقیت حذف شد.');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطا در حذف ربات: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle bot status
     */
    public function toggleStatus(TelegramBot $telegramBot)
    {
        $telegramBot->update(['is_active' => !$telegramBot->is_active]);

        return response()->json([
            'success' => true,
            'message' => $telegramBot->is_active ? 'ربات فعال شد.' : 'ربات غیرفعال شد.',
            'is_active' => $telegramBot->is_active
        ]);
    }

    /**
     * Test bot connection
     */
    public function test(TelegramBot $telegramBot)
    {
        try {
            $telegramService = new TelegramBotService($telegramBot);
            $botInfo = $telegramService->getBotInfo();

            if ($botInfo && $botInfo['ok']) {
                return response()->json([
                    'success' => true,
                    'message' => 'ربات به درستی کار می‌کند.',
                    'bot_info' => $botInfo['result']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'خطا در اتصال به ربات.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Reset webhook for bot
     */
    public function resetWebhook(TelegramBot $telegramBot)
    {
        try {
            $telegramService = new TelegramBotService($telegramBot);
            $result = $telegramService->setWebhook();

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Webhook با موفقیت تنظیم مجدد شد.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'خطا در تنظیم مجدد webhook.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا: ' . $e->getMessage()
            ]);
        }
    }
}
