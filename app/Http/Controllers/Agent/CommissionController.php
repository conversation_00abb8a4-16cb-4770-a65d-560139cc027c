<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Commission;
use Illuminate\Http\Request;

class CommissionController extends Controller
{
    public function index(Request $request)
    {
        $agent = auth()->user()->agent;
        
        $query = $agent->commissions()->with(['purchase.plan', 'purchase.user']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $commissions = $query->latest()->paginate(15);

        $stats = [
            'total_commission' => $agent->total_commission,
            'pending_commission' => $agent->commissions()->pending()->sum('amount'),
            'paid_commission' => $agent->commissions()->paid()->sum('amount'),
            'wallet_balance' => $agent->wallet_balance,
            'this_month_commission' => $agent->getCommissionForPeriod(now()->startOfMonth(), now()->endOfMonth()),
        ];

        return view('agent.commissions.index', compact('commissions', 'stats'));
    }

    public function show(Commission $commission)
    {
        // Make sure this commission belongs to the current agent
        if ($commission->agent_id !== auth()->user()->agent->id) {
            abort(403, 'دسترسی غیرمجاز');
        }

        $commission->load(['purchase.plan', 'purchase.user']);
        
        return view('agent.commissions.show', compact('commission'));
    }
}
