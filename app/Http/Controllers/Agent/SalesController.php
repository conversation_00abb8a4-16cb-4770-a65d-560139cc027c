<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Purchase;
use Illuminate\Http\Request;

class SalesController extends Controller
{
    public function index(Request $request)
    {
        $agent = auth()->user()->agent;
        
        $query = $agent->purchases()->with(['user', 'plan']);

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            })->orWhereHas('plan', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        $sales = $query->latest()->paginate(15);

        $stats = [
            'total_sales' => $agent->purchases()->where('status', 'completed')->sum('amount'),
            'total_count' => $agent->purchases()->where('status', 'completed')->count(),
            'this_month_sales' => $agent->getMonthlySales(now()->year, now()->month),
            'pending_sales' => $agent->purchases()->where('status', 'pending')->sum('amount'),
        ];

        return view('agent.sales.index', compact('sales', 'stats'));
    }

    public function show(Purchase $purchase)
    {
        // Make sure this purchase belongs to the current agent
        if ($purchase->agent_id !== auth()->user()->agent->id) {
            abort(403, 'دسترسی غیرمجاز');
        }

        $purchase->load(['user', 'plan', 'commission']);
        
        return view('agent.sales.show', compact('purchase'));
    }
}
