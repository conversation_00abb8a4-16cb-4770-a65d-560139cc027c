<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\TelegramBot;
use App\Services\TelegramBotService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TelegramBotController extends Controller
{
    public function __construct()
    {
        $this->middleware('agent');
    }

    /**
     * Show telegram bot management page
     */
    public function index()
    {
        $agent = Auth::user()->agent;
        $telegramBot = $agent->telegramBot;
        $stats = $agent->getTelegramStats();

        return view('agent.telegram.index', compact('agent', 'telegramBot', 'stats'));
    }

    /**
     * Store or update telegram bot configuration
     */
    public function store(Request $request)
    {
        $request->validate([
            'bot_token' => 'required|string',
            'bot_username' => 'required|string|regex:/^[a-zA-Z0-9_]+$/',
            'welcome_message' => 'nullable|string|max:4000',
            'mandatory_channel_username' => 'nullable|string|regex:/^[a-zA-Z0-9_]+$/',
            'mandatory_channel_id' => 'nullable|string',
            'mandatory_channel_title' => 'nullable|string|max:255',
        ]);

        $agent = Auth::user()->agent;

        try {
            // Test the bot token first
            $testService = new TelegramBotService();
            $testBot = new TelegramBot(['bot_token' => $request->bot_token]);
            $testService->setBot($testBot);
            $botInfo = $testService->getBotInfo();

            if (!$botInfo || !$botInfo['ok']) {
                return back()->withErrors(['bot_token' => 'توکن ربات نامعتبر است.']);
            }

            // Check if username matches
            $actualUsername = $botInfo['result']['username'];
            if (strtolower($actualUsername) !== strtolower($request->bot_username)) {
                return back()->withErrors([
                    'bot_username' => "نام کاربری ربات باید {$actualUsername} باشد."
                ]);
            }

            // Create or update telegram bot
            $telegramBot = TelegramBot::updateOrCreate(
                ['agent_id' => $agent->id],
                [
                    'bot_token' => $request->bot_token,
                    'bot_username' => $request->bot_username,
                    'welcome_message' => $request->welcome_message,
                    'mandatory_channel_username' => $request->mandatory_channel_username,
                    'mandatory_channel_id' => $request->mandatory_channel_id,
                    'mandatory_channel_title' => $request->mandatory_channel_title,
                    'is_active' => true,
                ]
            );

            // Set webhook
            $telegramService = new TelegramBotService($telegramBot);
            $webhookResult = $telegramService->setWebhook();

            if ($webhookResult) {
                return redirect()->route('agent.telegram.index')
                    ->with('success', 'ربات تلگرام با موفقیت تنظیم شد.');
            } else {
                return back()->withErrors(['webhook' => 'خطا در تنظیم webhook ربات.']);
            }

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'خطا در تنظیم ربات: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle bot status
     */
    public function toggleStatus(Request $request)
    {
        $agent = Auth::user()->agent;
        $telegramBot = $agent->telegramBot;

        if (!$telegramBot) {
            return response()->json(['success' => false, 'message' => 'ربات یافت نشد.']);
        }

        $telegramBot->update(['is_active' => !$telegramBot->is_active]);

        return response()->json([
            'success' => true,
            'message' => $telegramBot->is_active ? 'ربات فعال شد.' : 'ربات غیرفعال شد.',
            'is_active' => $telegramBot->is_active
        ]);
    }

    /**
     * Test bot connection
     */
    public function test(Request $request)
    {
        $agent = Auth::user()->agent;
        $telegramBot = $agent->telegramBot;

        if (!$telegramBot) {
            return response()->json(['success' => false, 'message' => 'ربات یافت نشد.']);
        }

        try {
            $telegramService = new TelegramBotService($telegramBot);
            $botInfo = $telegramService->getBotInfo();

            if ($botInfo && $botInfo['ok']) {
                return response()->json([
                    'success' => true,
                    'message' => 'ربات به درستی کار می‌کند.',
                    'bot_info' => $botInfo['result']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'خطا در اتصال به ربات.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Show telegram users
     */
    public function users(Request $request)
    {
        $agent = Auth::user()->agent;
        $telegramBot = $agent->telegramBot;

        if (!$telegramBot) {
            return redirect()->route('agent.telegram.index')
                ->with('error', 'ابتدا ربات تلگرام را تنظیم کنید.');
        }

        $query = $telegramBot->telegramUsers()->latest('last_interaction');

        // Filter by registration status
        if ($request->has('registered')) {
            if ($request->registered === '1') {
                $query->where('is_registered', true);
            } elseif ($request->registered === '0') {
                $query->where('is_registered', false);
            }
        }

        // Filter by activity
        if ($request->has('active') && $request->active === '1') {
            $query->active();
        }

        // Search
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('telegram_username', 'like', "%{$search}%")
                  ->orWhere('telegram_user_id', 'like', "%{$search}%");
            });
        }

        $users = $query->paginate(20);
        $stats = $agent->getTelegramStats();

        return view('agent.telegram.users', compact('users', 'stats', 'telegramBot'));
    }

    /**
     * Send message to user
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:telegram_users,id',
            'message' => 'required|string|max:4000',
        ]);

        $agent = Auth::user()->agent;
        $telegramBot = $agent->telegramBot;

        if (!$telegramBot) {
            return response()->json(['success' => false, 'message' => 'ربات یافت نشد.']);
        }

        $telegramUser = $telegramBot->telegramUsers()->find($request->user_id);
        
        if (!$telegramUser) {
            return response()->json(['success' => false, 'message' => 'کاربر یافت نشد.']);
        }

        try {
            $telegramService = new TelegramBotService($telegramBot);
            $result = $telegramService->sendMessage(
                $telegramUser->telegram_user_id,
                $request->message
            );

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'پیام با موفقیت ارسال شد.'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'خطا در ارسال پیام.'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطا: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Broadcast message to all users
     */
    public function broadcast(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:4000',
            'target' => 'required|in:all,active,registered',
        ]);

        $agent = Auth::user()->agent;
        $telegramBot = $agent->telegramBot;

        if (!$telegramBot) {
            return response()->json(['success' => false, 'message' => 'ربات یافت نشد.']);
        }

        $query = $telegramBot->telegramUsers();

        // Apply filters based on target
        switch ($request->target) {
            case 'active':
                $query->active();
                break;
            case 'registered':
                $query->registered();
                break;
            // 'all' doesn't need additional filtering
        }

        $users = $query->get();
        $telegramService = new TelegramBotService($telegramBot);
        
        $successCount = 0;
        $failCount = 0;

        foreach ($users as $user) {
            try {
                $result = $telegramService->sendMessage(
                    $user->telegram_user_id,
                    $request->message
                );
                
                if ($result) {
                    $successCount++;
                } else {
                    $failCount++;
                }
                
                // Add small delay to avoid rate limiting
                usleep(100000); // 0.1 second
                
            } catch (\Exception $e) {
                $failCount++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "پیام به {$successCount} کاربر ارسال شد. {$failCount} ارسال ناموفق.",
            'stats' => [
                'success' => $successCount,
                'failed' => $failCount,
                'total' => $users->count()
            ]
        ]);
    }
}
