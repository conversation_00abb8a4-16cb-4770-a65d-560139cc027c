<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\WithdrawalRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WithdrawalController extends Controller
{
    public function index(Request $request)
    {
        $agent = auth()->user()->agent;
        
        $query = $agent->withdrawalRequests();

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        $withdrawals = $query->latest()->paginate(15);

        $stats = [
            'wallet_balance' => $agent->wallet_balance,
            'total_withdrawn' => $agent->withdrawalRequests()->completed()->sum('amount'),
            'pending_amount' => $agent->withdrawalRequests()->pending()->sum('amount'),
            'min_withdrawal' => $agent->getMinWithdrawalAmount(),
        ];

        return view('agent.withdrawals.index', compact('withdrawals', 'stats'));
    }

    public function create()
    {
        $agent = auth()->user()->agent;
        
        if ($agent->wallet_balance < $agent->getMinWithdrawalAmount()) {
            return redirect()->route('agent.withdrawals.index')
                ->with('error', 'موجودی شما کمتر از حداقل مبلغ برداشت است.');
        }

        return view('agent.withdrawals.create', compact('agent'));
    }

    public function store(Request $request)
    {
        $agent = auth()->user()->agent;
        
        $request->validate([
            'amount' => 'required|numeric|min:' . $agent->getMinWithdrawalAmount(),
            'method' => 'required|in:bank,crypto',
            'agent_notes' => 'nullable|string|max:500',
            
            // Bank fields
            'bank_account_number' => 'required_if:method,bank|string|max:50',
            'bank_account_name' => 'required_if:method,bank|string|max:255',
            'bank_name' => 'required_if:method,bank|string|max:255',
            
            // Crypto fields
            'crypto_wallet_type' => 'required_if:method,crypto|string|in:' . implode(',', array_keys(WithdrawalRequest::CRYPTO_TYPES)),
            'crypto_wallet_address' => 'required_if:method,crypto|string|max:255',
            'crypto_network' => 'required_if:method,crypto|string|in:' . implode(',', array_keys(WithdrawalRequest::CRYPTO_NETWORKS)),
        ]);

        if (!$agent->canWithdraw($request->amount)) {
            return redirect()->back()
                ->with('error', 'موجودی شما برای این مبلغ کافی نیست.')
                ->withInput();
        }

        // Check for pending withdrawal requests
        $pendingAmount = $agent->withdrawalRequests()->pending()->sum('amount');
        if (($pendingAmount + $request->amount) > $agent->wallet_balance) {
            return redirect()->back()
                ->with('error', 'مجموع درخواست‌های در انتظار و درخواست جدید از موجودی شما بیشتر است.')
                ->withInput();
        }

        try {
            DB::beginTransaction();

            $withdrawalData = [
                'agent_id' => $agent->id,
                'amount' => $request->amount,
                'method' => $request->method,
                'agent_notes' => $request->agent_notes,
                'status' => WithdrawalRequest::STATUS_PENDING,
            ];

            if ($request->method === 'bank') {
                $withdrawalData = array_merge($withdrawalData, [
                    'bank_account_number' => $request->bank_account_number,
                    'bank_account_name' => $request->bank_account_name,
                    'bank_name' => $request->bank_name,
                ]);
            } else {
                $withdrawalData = array_merge($withdrawalData, [
                    'crypto_wallet_type' => $request->crypto_wallet_type,
                    'crypto_wallet_address' => $request->crypto_wallet_address,
                    'crypto_network' => $request->crypto_network,
                ]);
            }

            WithdrawalRequest::create($withdrawalData);

            DB::commit();

            return redirect()->route('agent.withdrawals.index')
                ->with('success', 'درخواست تسویه حساب شما با موفقیت ثبت شد.');

        } catch (\Exception $e) {
            DB::rollBack();
            
            return redirect()->back()
                ->with('error', 'خطا در ثبت درخواست. لطفاً دوباره تلاش کنید.')
                ->withInput();
        }
    }

    public function show(WithdrawalRequest $withdrawal)
    {
        // Make sure this withdrawal belongs to the current agent
        if ($withdrawal->agent_id !== auth()->user()->agent->id) {
            abort(403, 'دسترسی غیرمجاز');
        }

        return view('agent.withdrawals.show', compact('withdrawal'));
    }

    public function cancel(WithdrawalRequest $withdrawal)
    {
        // Make sure this withdrawal belongs to the current agent
        if ($withdrawal->agent_id !== auth()->user()->agent->id) {
            abort(403, 'دسترسی غیرمجاز');
        }

        if (!$withdrawal->isPending()) {
            return redirect()->back()
                ->with('error', 'فقط درخواست‌های در انتظار قابل لغو هستند.');
        }

        $withdrawal->update([
            'status' => WithdrawalRequest::STATUS_REJECTED,
            'admin_notes' => 'لغو شده توسط نماینده',
        ]);

        return redirect()->route('agent.withdrawals.index')
            ->with('success', 'درخواست تسویه حساب لغو شد.');
    }

    public function walletSettings()
    {
        $agent = auth()->user()->agent;
        return view('agent.wallet-settings', compact('agent'));
    }

    public function updateWalletSettings(Request $request)
    {
        $agent = auth()->user()->agent;
        
        $request->validate([
            'crypto_wallet_type' => 'nullable|string|in:' . implode(',', array_keys(WithdrawalRequest::CRYPTO_TYPES)),
            'crypto_wallet_address' => 'nullable|string|max:255',
            'crypto_network' => 'nullable|string|in:' . implode(',', array_keys(WithdrawalRequest::CRYPTO_NETWORKS)),
            'bank_account_number' => 'nullable|string|max:50',
            'bank_account_name' => 'nullable|string|max:255',
            'bank_name' => 'nullable|string|max:255',
        ]);

        $agent->update([
            'crypto_wallet_type' => $request->crypto_wallet_type,
            'crypto_wallet_address' => $request->crypto_wallet_address,
            'crypto_network' => $request->crypto_network,
            'bank_account_number' => $request->bank_account_number,
            'bank_account_name' => $request->bank_account_name,
            'bank_name' => $request->bank_name,
        ]);

        return redirect()->route('agent.wallet-settings')
            ->with('success', 'تنظیمات کیف پول با موفقیت به‌روزرسانی شد.');
    }
}
