<?php

namespace App\Http\Controllers\Agent;

use App\Http\Controllers\Controller;
use App\Models\Purchase;
use App\Models\Commission;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $agent = auth()->user()->agent;
        
        if (!$agent) {
            abort(404, 'پروفایل نماینده یافت نشد.');
        }

        $stats = [
            'total_sales' => $agent->total_sales,
            'total_commission' => $agent->total_commission,
            'wallet_balance' => $agent->wallet_balance,
            'pending_commission' => $agent->commissions()->pending()->sum('amount'),
            'this_month_sales' => $agent->getMonthlySales(now()->year, now()->month),
            'this_month_commission' => $agent->getCommissionForPeriod(now()->startOfMonth(), now()->endOfMonth()),
            'total_customers' => $agent->purchases()->distinct('user_id')->count(),
            'referral_code' => $agent->referral_code,
        ];

        // Recent sales
        $recent_sales = $agent->purchases()
            ->with(['user', 'plan'])
            ->latest()
            ->limit(10)
            ->get();

        // Monthly sales chart data for current year
        $monthly_sales = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthly_sales[$month] = $agent->getMonthlySales(now()->year, $month);
        }

        // Recent commissions
        $recent_commissions = $agent->commissions()
            ->with(['purchase.plan'])
            ->latest()
            ->limit(5)
            ->get();

        return view('agent.dashboard', compact('stats', 'recent_sales', 'monthly_sales', 'recent_commissions'));
    }

    public function referralLink()
    {
        $agent = auth()->user()->agent;
        $referralLink = url('/register?ref=' . $agent->referral_code);
        
        return view('agent.referral-link', compact('referralLink', 'agent'));
    }
}
