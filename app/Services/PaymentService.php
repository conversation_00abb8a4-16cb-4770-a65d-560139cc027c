<?php

namespace App\Services;

use App\Models\Purchase;
use App\Models\Transaction;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    private $apiKey;
    private $gatewayUrl;
    private $callbackUrl;

    public function __construct()
    {
        $this->apiKey = config('services.payment_gateway.api_key', 'cDT+fuPZiQQFcq74E96oyDmnUfLJqylpPT');
        $this->gatewayUrl = config('services.payment_gateway.url', 'https://skypay1ng.nitropardazesh.site/api/request');
        $this->callbackUrl = config('services.payment_gateway.callback_url', url('/api/payment/callback'));
    }

    /**
     * Create payment request and get gateway URL
     */
    public function createPaymentRequest(Purchase $purchase): array
    {
        $token = Str::random(10);
        $orderId = time();
        $invoiceId = time();

        $data = [
            'Api_key' => $this->apiKey,
            'user_id' => $purchase->user_id,
            'email' => $purchase->user->email,
            'amount' => (int) $purchase->amount * 10, // Convert to integer
            'description' => 'خرید پلن ' . $purchase->plan->name,
            'callback' => $this->callbackUrl,
            'invoice_id' => $invoiceId,
        ];

        try {
            $response = $this->curlPost($this->gatewayUrl, $data);

            // Check if response starts with "Error Code:" - this indicates an error from gateway
            if (strpos($response, 'Error Code:') === 0) {
                Log::error('Payment gateway error response', [
                    'purchase_id' => $purchase->id,
                    'request_data' => $data,
                    'raw_response' => $response
                ]);

                return [
                    'success' => false,
                    'message' => 'خطا از درگاه پرداخت: ' . $response,
                    'debug' => [
                        'raw_response' => $response,
                        'request_data' => $data
                    ]
                ];
            }

            $responseObj = json_decode($response, true);

            Log::info('Payment gateway request', [
                'purchase_id' => $purchase->id,
                'data' => $data,
                'response' => $responseObj,
                'raw_response' => $response
            ]);

            if (isset($responseObj['success']) && ($responseObj['success'] == true || $responseObj['success'] == 'true')) {
                // Update purchase with gateway information
                $purchase->update([
                    'gateway_token' => $token,
                    'gateway_order_id' => $orderId,
                    'gateway_trans_id' => $responseObj['data']['TransId'] ?? null,
                    'gateway_url' => $responseObj['data']['TransId'] ?? null,
                    'gateway_response' => $responseObj,
                    'transaction_id' => $token,
                ]);

                // Create transaction record
                Transaction::create([
                    'user_id' => $purchase->user_id,
                    'type' => Transaction::TYPE_PURCHASE,
                    'amount' => $purchase->amount,
                    'status' => Transaction::STATUS_PENDING,
                    'description' => 'خرید پلن ' . $purchase->plan->name . ' - در انتظار پرداخت',
                    'reference_id' => $purchase->id,
                    'metadata' => [
                        'purchase_id' => $purchase->id,
                        'plan_name' => $purchase->plan->name,
                        'gateway_token' => $token,
                        'gateway_order_id' => $orderId,
                    ]
                ]);

                return [
                    'success' => true,
                    'payment_url' => $responseObj['data']['TransId'],
                    'token' => $token,
                    'order_id' => $orderId,
                ];
            } else {
                Log::error('Payment gateway error', [
                    'purchase_id' => $purchase->id,
                    'request_data' => $data,
                    'response' => $responseObj,
                    'raw_response' => $response
                ]);

                return [
                    'success' => false,
                    'message' => $responseObj['message'] ?? 'خطا در ایجاد درخواست پرداخت',
                    'debug' => [
                        'response' => $responseObj,
                        'raw_response' => $response
                    ]
                ];
            }

        } catch (\Exception $e) {
            Log::error('Payment gateway exception', [
                'purchase_id' => $purchase->id,
                'request_data' => $data,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'خطا در اتصال به درگاه پرداخت: ' . $e->getMessage(),
                'debug' => [
                    'error' => $e->getMessage(),
                    'request_data' => $data
                ]
            ];
        }
    }

    /**
     * Verify payment callback
     */
    public function verifyPayment(array $callbackData): array
    {
        // Find purchase by gateway token or transaction ID
        $purchase = Purchase::where('gateway_token', $callbackData['token'] ?? null)
            ->orWhere('gateway_trans_id', $callbackData['trans_id'] ?? null)
            ->first();

        if (!$purchase) {
            return [
                'success' => false,
                'message' => 'تراکنش یافت نشد',
            ];
        }

        // Update purchase with callback data
        $purchase->update([
            'gateway_response' => array_merge($purchase->gateway_response ?? [], [
                'callback' => $callbackData
            ])
        ]);

        // Check if payment was successful
        if (isset($callbackData['status']) && $callbackData['status'] == 'success') {
            $purchase->markAsCompleted();

            // Update transaction status
            Transaction::where('reference_id', $purchase->id)
                ->where('type', Transaction::TYPE_PURCHASE)
                ->update([
                    'status' => Transaction::STATUS_COMPLETED,
                    'description' => 'خرید پلن ' . $purchase->plan->name . ' - پرداخت موفق',
                ]);

            return [
                'success' => true,
                'message' => 'پرداخت با موفقیت انجام شد',
                'purchase' => $purchase,
            ];
        } else {
            $purchase->update(['status' => Purchase::STATUS_FAILED]);

            // Update transaction status
            Transaction::where('reference_id', $purchase->id)
                ->where('type', Transaction::TYPE_PURCHASE)
                ->update([
                    'status' => Transaction::STATUS_FAILED,
                    'description' => 'خرید پلن ' . $purchase->plan->name . ' - پرداخت ناموفق',
                ]);

            return [
                'success' => false,
                'message' => 'پرداخت ناموفق بود',
            ];
        }
    }

    /**
     * cURL POST request helper
     */
    private function curlPost(string $url, array $data): string
    {
        try {
            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/x-www-form-urlencoded',
            ])
            ->asForm() // معادل http_build_query
            ->timeout(30)
            ->post($url, $data);
    
            if ($response->failed()) {
                throw new \Exception('HTTP Error: ' . $response->body());
            }
    
            return $response->body(); // همون خروجی string مثل curl_exec
        } catch (\Exception $e) {
            throw new \Exception('HTTP Error: ' . $e->getMessage());
        }
    }
}
