<?php

namespace App\Services;

use App\Models\Purchase;
use App\Models\Commission;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;

class CommissionService
{
    /**
     * Calculate and create commission for a purchase
     */
    public function calculateCommission(Purchase $purchase): ?Commission
    {
        if (!$purchase->agent_id || $purchase->commission_amount <= 0) {
            return null;
        }

        // Check if commission already exists
        if ($purchase->commission) {
            return $purchase->commission;
        }

        return Commission::create([
            'agent_id' => $purchase->agent_id,
            'purchase_id' => $purchase->id,
            'amount' => $purchase->commission_amount,
            'status' => Commission::STATUS_PENDING,
        ]);
    }

    /**
     * Process commission payment
     */
    public function payCommission(Commission $commission, string $notes = null): bool
    {
        if (!$commission->isPending()) {
            return false;
        }

        try {
            DB::beginTransaction();

            // Mark commission as paid
            $commission->update([
                'status' => Commission::STATUS_PAID,
                'paid_at' => now(),
                'notes' => $notes,
            ]);

            // Update agent's wallet and statistics
            $agent = $commission->agent;
            $agent->increment('wallet_balance', $commission->amount);
            $agent->increment('total_commission', $commission->amount);

            // Create transaction record
            Transaction::create([
                'user_id' => $agent->user_id,
                'agent_id' => $agent->id,
                'type' => Transaction::TYPE_COMMISSION,
                'amount' => $commission->amount,
                'status' => Transaction::STATUS_COMPLETED,
                'description' => 'کمیسیون فروش پلن ' . $commission->purchase->plan->name,
                'reference_id' => $commission->id,
                'metadata' => [
                    'purchase_id' => $commission->purchase_id,
                    'plan_name' => $commission->purchase->plan->name,
                    'customer_name' => $commission->purchase->user->name,
                ]
            ]);

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }

    /**
     * Process multiple commission payments
     */
    public function bulkPayCommissions(array $commissionIds, string $notes = null): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'total_amount' => 0,
        ];

        $commissions = Commission::whereIn('id', $commissionIds)
            ->where('status', Commission::STATUS_PENDING)
            ->get();

        foreach ($commissions as $commission) {
            if ($this->payCommission($commission, $notes)) {
                $results['success']++;
                $results['total_amount'] += $commission->amount;
            } else {
                $results['failed']++;
            }
        }

        return $results;
    }

    /**
     * Cancel commission
     */
    public function cancelCommission(Commission $commission, string $reason): bool
    {
        if (!$commission->isPending()) {
            return false;
        }

        $commission->update([
            'status' => Commission::STATUS_CANCELLED,
            'notes' => $reason,
        ]);

        return true;
    }

    /**
     * Get commission statistics for an agent
     */
    public function getAgentCommissionStats(int $agentId): array
    {
        $agent = \App\Models\Agent::find($agentId);
        
        if (!$agent) {
            return [];
        }

        return [
            'total_commission' => $agent->total_commission,
            'wallet_balance' => $agent->wallet_balance,
            'pending_commission' => $agent->commissions()->pending()->sum('amount'),
            'paid_commission' => $agent->commissions()->paid()->sum('amount'),
            'this_month_commission' => $agent->getCommissionForPeriod(
                now()->startOfMonth(), 
                now()->endOfMonth()
            ),
            'last_month_commission' => $agent->getCommissionForPeriod(
                now()->subMonth()->startOfMonth(), 
                now()->subMonth()->endOfMonth()
            ),
        ];
    }

    /**
     * Get system-wide commission statistics
     */
    public function getSystemCommissionStats(): array
    {
        return [
            'total_pending' => Commission::pending()->sum('amount'),
            'total_paid' => Commission::paid()->sum('amount'),
            'total_cancelled' => Commission::where('status', Commission::STATUS_CANCELLED)->sum('amount'),
            'this_month_paid' => Commission::paid()
                ->whereMonth('paid_at', now()->month)
                ->whereYear('paid_at', now()->year)
                ->sum('amount'),
            'pending_count' => Commission::pending()->count(),
            'paid_count' => Commission::paid()->count(),
        ];
    }
}
