<?php

namespace App\Services;

use App\Models\WithdrawalRequest;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WithdrawalService
{
    /**
     * Create a new withdrawal request
     */
    public function createWithdrawalRequest(array $data): WithdrawalRequest
    {
        return DB::transaction(function () use ($data) {
            $withdrawal = WithdrawalRequest::create($data);
            
            Log::info('Withdrawal request created', [
                'withdrawal_id' => $withdrawal->id,
                'agent_id' => $withdrawal->agent_id,
                'amount' => $withdrawal->amount,
                'method' => $withdrawal->method
            ]);
            
            return $withdrawal;
        });
    }

    /**
     * Approve withdrawal request
     */
    public function approveWithdrawal(WithdrawalRequest $withdrawal, string $notes = null): bool
    {
        if (!$withdrawal->isPending()) {
            return false;
        }

        $withdrawal->update([
            'status' => WithdrawalRequest::STATUS_APPROVED,
            'admin_notes' => $notes,
        ]);

        Log::info('Withdrawal request approved', [
            'withdrawal_id' => $withdrawal->id,
            'agent_id' => $withdrawal->agent_id,
            'amount' => $withdrawal->amount
        ]);

        return true;
    }

    /**
     * Reject withdrawal request
     */
    public function rejectWithdrawal(WithdrawalRequest $withdrawal, string $reason): bool
    {
        if (!$withdrawal->isPending()) {
            return false;
        }

        $withdrawal->update([
            'status' => WithdrawalRequest::STATUS_REJECTED,
            'admin_notes' => $reason,
        ]);

        Log::info('Withdrawal request rejected', [
            'withdrawal_id' => $withdrawal->id,
            'agent_id' => $withdrawal->agent_id,
            'amount' => $withdrawal->amount,
            'reason' => $reason
        ]);

        return true;
    }

    /**
     * Complete withdrawal request
     */
    public function completeWithdrawal(WithdrawalRequest $withdrawal, array $data = []): bool
    {
        if (!in_array($withdrawal->status, [WithdrawalRequest::STATUS_APPROVED, WithdrawalRequest::STATUS_PROCESSING])) {
            return false;
        }

        // Check if agent has enough balance
        if ($withdrawal->agent->wallet_balance < $withdrawal->amount) {
            Log::error('Insufficient balance for withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'required_amount' => $withdrawal->amount,
                'available_balance' => $withdrawal->agent->wallet_balance
            ]);
            return false;
        }

        try {
            DB::beginTransaction();

            // Update withdrawal request
            $withdrawal->update([
                'status' => WithdrawalRequest::STATUS_COMPLETED,
                'transaction_hash' => $data['transaction_hash'] ?? null,
                'admin_notes' => $data['admin_notes'] ?? null,
                'processed_at' => now(),
            ]);

            // Deduct amount from agent's wallet
            $withdrawal->agent->decrement('wallet_balance', $withdrawal->amount);

            // Create transaction record
            Transaction::create([
                'user_id' => $withdrawal->agent->user_id,
                'agent_id' => $withdrawal->agent_id,
                'type' => Transaction::TYPE_WITHDRAWAL,
                'amount' => $withdrawal->amount,
                'status' => Transaction::STATUS_COMPLETED,
                'description' => 'برداشت از کیف پول - ' . $withdrawal->method_in_persian,
                'reference_id' => $withdrawal->id,
                'metadata' => [
                    'method' => $withdrawal->method,
                    'crypto_type' => $withdrawal->crypto_wallet_type,
                    'crypto_address' => $withdrawal->crypto_wallet_address,
                    'crypto_network' => $withdrawal->crypto_network,
                    'transaction_hash' => $data['transaction_hash'] ?? null,
                    'bank_account' => $withdrawal->bank_account_number,
                ]
            ]);

            DB::commit();

            Log::info('Withdrawal request completed', [
                'withdrawal_id' => $withdrawal->id,
                'agent_id' => $withdrawal->agent_id,
                'amount' => $withdrawal->amount,
                'transaction_hash' => $data['transaction_hash'] ?? null
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to complete withdrawal', [
                'withdrawal_id' => $withdrawal->id,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Bulk approve withdrawal requests
     */
    public function bulkApprove(array $withdrawalIds, string $notes = null): array
    {
        $results = [
            'success' => 0,
            'failed' => 0,
            'total_amount' => 0,
        ];

        $withdrawals = WithdrawalRequest::whereIn('id', $withdrawalIds)
            ->where('status', WithdrawalRequest::STATUS_PENDING)
            ->get();

        foreach ($withdrawals as $withdrawal) {
            if ($this->approveWithdrawal($withdrawal, $notes)) {
                $results['success']++;
                $results['total_amount'] += $withdrawal->amount;
            } else {
                $results['failed']++;
            }
        }

        return $results;
    }

    /**
     * Get withdrawal statistics
     */
    public function getWithdrawalStats(): array
    {
        return [
            'pending_amount' => WithdrawalRequest::pending()->sum('amount'),
            'pending_count' => WithdrawalRequest::pending()->count(),
            'completed_amount' => WithdrawalRequest::completed()->sum('amount'),
            'completed_count' => WithdrawalRequest::completed()->count(),
            'crypto_pending' => WithdrawalRequest::pending()->crypto()->sum('amount'),
            'bank_pending' => WithdrawalRequest::pending()->bank()->sum('amount'),
            'this_month_completed' => WithdrawalRequest::completed()
                ->whereMonth('processed_at', now()->month)
                ->whereYear('processed_at', now()->year)
                ->sum('amount'),
        ];
    }

    /**
     * Get agent withdrawal statistics
     */
    public function getAgentWithdrawalStats(int $agentId): array
    {
        $agent = \App\Models\Agent::find($agentId);
        
        if (!$agent) {
            return [];
        }

        return [
            'wallet_balance' => $agent->wallet_balance,
            'total_withdrawn' => $agent->withdrawalRequests()->completed()->sum('amount'),
            'pending_amount' => $agent->withdrawalRequests()->pending()->sum('amount'),
            'min_withdrawal' => $agent->getMinWithdrawalAmount(),
            'can_withdraw' => $agent->wallet_balance >= $agent->getMinWithdrawalAmount(),
            'available_balance' => max(0, $agent->wallet_balance - $agent->withdrawalRequests()->pending()->sum('amount')),
        ];
    }

    /**
     * Validate crypto wallet address (basic validation)
     */
    public function validateCryptoAddress(string $address, string $type): bool
    {
        switch (strtoupper($type)) {
            case 'BTC':
                return $this->validateBitcoinAddress($address);
            case 'ETH':
            case 'USDT':
            case 'USDC':
                return $this->validateEthereumAddress($address);
            case 'TRX':
                return $this->validateTronAddress($address);
            default:
                return strlen($address) >= 20; // Basic length check
        }
    }

    /**
     * Basic Bitcoin address validation
     */
    private function validateBitcoinAddress(string $address): bool
    {
        return preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) ||
               preg_match('/^bc1[a-z0-9]{39,59}$/', $address);
    }

    /**
     * Basic Ethereum address validation
     */
    private function validateEthereumAddress(string $address): bool
    {
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
    }

    /**
     * Basic TRON address validation
     */
    private function validateTronAddress(string $address): bool
    {
        return preg_match('/^T[A-Za-z1-9]{33}$/', $address);
    }
}
