<?php

namespace App\Observers;

use App\Models\WithdrawalRequest;
use App\Notifications\WithdrawalStatusNotification;

class WithdrawalRequestObserver
{
    /**
     * Handle the WithdrawalRequest "updated" event.
     */
    public function updated(WithdrawalRequest $withdrawalRequest): void
    {
        // Check if status was changed
        if ($withdrawalRequest->wasChanged('status')) {
            $newStatus = $withdrawalRequest->status;
            
            // Send notification to agent
            $withdrawalRequest->agent->user->notify(
                new WithdrawalStatusNotification($withdrawalRequest, $newStatus)
            );
        }
    }
}
