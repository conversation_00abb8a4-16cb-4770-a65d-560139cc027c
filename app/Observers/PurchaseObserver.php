<?php

namespace App\Observers;

use App\Models\Purchase;
use App\Services\CommissionService;

class PurchaseObserver
{
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    /**
     * Handle the Purchase "updated" event.
     */
    public function updated(Purchase $purchase): void
    {
        // If purchase status changed to completed, create commission
        if ($purchase->wasChanged('status') && $purchase->status === Purchase::STATUS_COMPLETED) {
            $this->commissionService->calculateCommission($purchase);
        }
    }
}
