# 🚀 Next.js Integration Example

## API Endpoint
```
POST/GET https://provider.nitropardazesh.site/api/nextjs/telegram-auth
```

## 📱 Complete Next.js Example

### 1. Install Dependencies
```bash
npm install axios
```

### 2. Add Telegram Script to _document.js
```javascript
// pages/_document.js
import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="fa" dir="rtl">
      <Head>
        <script src="https://telegram.org/js/telegram-web-app.js"></script>
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
```

### 3. Main Page Component
```javascript
// pages/index.js
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import axios from 'axios';

export default function Home() {
  const router = useRouter();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const authenticateUser = async () => {
      try {
        // Get agent_id from URL
        const { agent_id } = router.query;
        if (!agent_id) {
          throw new Error('Agent ID is required');
        }

        // Initialize Telegram WebApp
        const tg = window.Telegram?.WebApp;
        if (!tg) {
          throw new Error('Telegram WebApp not available');
        }

        tg.ready();
        tg.expand();

        // Get user data from Telegram
        const telegramUser = tg.initDataUnsafe?.user;
        if (!telegramUser) {
          throw new Error('Telegram user data not available');
        }

        // Prepare auth data
        const authData = {
          telegram_id: telegramUser.id,
          first_name: telegramUser.first_name,
          last_name: telegramUser.last_name || '',
          username: telegramUser.username || '',
          photo_url: telegramUser.photo_url || '',
          agent_id: parseInt(agent_id)
        };

        console.log('Sending auth data:', authData);

        // Send auth request
        const response = await axios.post(
          'https://provider.nitropardazesh.site/api/nextjs/telegram-auth',
          authData,
          {
            headers: {
              'Content-Type': 'application/json',
            }
          }
        );

        if (response.data.success) {
          const { user: userData, token, is_new_user } = response.data.data;
          
          // Store token in localStorage
          localStorage.setItem('auth_token', token);
          
          // Set user data
          setUser(userData);
          
          console.log(is_new_user ? 'User registered successfully!' : 'User logged in successfully!');
        } else {
          throw new Error(response.data.message || 'Authentication failed');
        }

      } catch (err) {
        console.error('Auth error:', err);
        setError(err.response?.data?.message || err.message);
      } finally {
        setLoading(false);
      }
    };

    if (router.isReady) {
      authenticateUser();
    }
  }, [router.isReady, router.query]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
        <div className="text-center text-white">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-xl">در حال ورود...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-500 to-pink-600">
        <div className="text-center text-white p-8">
          <div className="text-6xl mb-4">❌</div>
          <h1 className="text-2xl font-bold mb-4">خطا در ورود</h1>
          <p className="mb-6">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-white text-red-500 px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors"
          >
            تلاش مجدد
          </button>
        </div>
      </div>
    );
  }

  if (user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-500 to-blue-600 p-4">
        <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="p-6">
            <div className="text-center mb-6">
              {user.profile_photo_url && (
                <img 
                  src={user.profile_photo_url} 
                  alt="Profile" 
                  className="w-20 h-20 rounded-full mx-auto mb-4"
                />
              )}
              <h1 className="text-2xl font-bold text-gray-800">
                سلام {user.name}! 👋
              </h1>
              <p className="text-gray-600">خوش آمدید به سیستم ما</p>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">نام:</span>
                <span className="font-medium">{user.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">ایمیل:</span>
                <span className="font-medium">{user.email}</span>
              </div>
              {user.telegram_username && (
                <div className="flex justify-between">
                  <span className="text-gray-500">یوزرنیم:</span>
                  <span className="font-medium">@{user.telegram_username}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-500">شناسه تلگرام:</span>
                <span className="font-medium">{user.telegram_id}</span>
              </div>
            </div>

            <div className="mt-6 space-y-3">
              <button 
                onClick={() => router.push('/plans')}
                className="w-full bg-blue-500 text-white py-3 rounded-lg font-bold hover:bg-blue-600 transition-colors"
              >
                🛒 مشاهده پلن‌ها
              </button>
              
              <button 
                onClick={() => router.push('/dashboard')}
                className="w-full bg-green-500 text-white py-3 rounded-lg font-bold hover:bg-green-600 transition-colors"
              >
                📊 پنل کاربری
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
}
```

### 4. API Client Setup (Optional)
```javascript
// lib/api.js
import axios from 'axios';

const API_BASE_URL = 'https://provider.nitropardazesh.site/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export default apiClient;
```

### 5. Protected Route Example
```javascript
// pages/dashboard.js
import { useEffect, useState } from 'react';
import apiClient from '../lib/api';

export default function Dashboard() {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await apiClient.get('/nextjs/profile');
        if (response.data.success) {
          setProfile(response.data.data);
        }
      } catch (error) {
        console.error('Failed to fetch profile:', error);
        // Redirect to login if token is invalid
        window.location.href = '/';
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-4">
      <h1>Dashboard</h1>
      {profile && (
        <div>
          <p>Welcome, {profile.user.name}!</p>
          <p>Agent: {profile.agent?.name}</p>
        </div>
      )}
    </div>
  );
}
```

## 🔧 Key Features

✅ **Unified Auth** - One endpoint for both register and login  
✅ **Automatic Detection** - Checks if user exists and acts accordingly  
✅ **Token-based** - Returns JWT token for subsequent requests  
✅ **User Data** - Automatically extracts from Telegram WebApp  
✅ **Agent Linking** - Links user to specific agent  
✅ **Error Handling** - Comprehensive error responses  

## 📝 URL Structure

The Telegram bot will send users to URLs like:
```
https://miniapp.nitropardazesh.site?agent_id=1&telegram_id=123456789&first_name=John&last_name=Doe&username=johndoe
```

Your Next.js app should extract these parameters and use them for authentication.

## 🚀 Response Format

```json
{
  "success": true,
  "message": "User registered and logged in successfully",
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "telegram_id": 123456789,
      "telegram_username": "johndoe",
      "profile_photo_url": "https://t.me/i/userpic/320/johndoe.jpg"
    },
    "agent": {
      "id": 1,
      "name": "Agent Name",
      "referral_code": "AGT001"
    },
    "token": "1|abcdef123456...",
    "is_new_user": true
  }
}
```

## 🎯 Next Steps

1. Deploy your Next.js app to `https://miniapp.nitropardazesh.site`
2. Test the authentication flow
3. Build your app features (plans, dashboard, etc.)
4. Use the token for authenticated API calls
