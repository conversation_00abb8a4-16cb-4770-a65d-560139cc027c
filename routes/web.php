<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\AgentController as AdminAgentController;
use App\Http\Controllers\Admin\PlanController as AdminPlanController;
use App\Http\Controllers\Admin\PurchaseController as AdminPurchaseController;
use App\Http\Controllers\Admin\CommissionController as AdminCommissionController;
use App\Http\Controllers\Agent\DashboardController as AgentDashboardController;
use App\Http\Controllers\Agent\SalesController as AgentSalesController;
use App\Http\Controllers\Agent\CommissionController as AgentCommissionController;
use App\Http\Controllers\Agent\WithdrawalController as AgentWithdrawalController;
use App\Http\Controllers\Admin\WithdrawalController as AdminWithdrawalController;
use App\Http\Controllers\Auth\LoginController;

Route::get('/', function () {
    return redirect()->route('plans.index');
});

// Public plans routes
Route::get('/plans', [App\Http\Controllers\PlanController::class, 'index'])->name('plans.index');
Route::get('/plans/{plan}', [App\Http\Controllers\PlanController::class, 'show'])->name('plans.show');

// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login')->middleware('guest');
Route::post('/login', [LoginController::class, 'login'])->middleware('guest');
Route::post('/logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Telegram authentication
Route::get('/telegram-auth', function () {
    return view('telegram-auth');
})->name('telegram-auth');

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Agents management
    Route::resource('agents', AdminAgentController::class);

    // Plans management
    Route::resource('plans', AdminPlanController::class);

    // Purchases management
    Route::resource('purchases', AdminPurchaseController::class);
    Route::post('purchases/{purchase}/extend', [AdminPurchaseController::class, 'extend'])->name('purchases.extend');
    Route::post('purchases/{purchase}/toggle-status', [AdminPurchaseController::class, 'toggleStatus'])->name('purchases.toggle-status');

    // Commissions management
    Route::get('commissions', [AdminCommissionController::class, 'index'])->name('commissions.index');
    Route::get('commissions/{commission}', [AdminCommissionController::class, 'show'])->name('commissions.show');
    Route::post('commissions/{commission}/pay', [AdminCommissionController::class, 'pay'])->name('commissions.pay');
    Route::post('commissions/{commission}/cancel', [AdminCommissionController::class, 'cancel'])->name('commissions.cancel');
    Route::post('commissions/bulk-pay', [AdminCommissionController::class, 'bulkPay'])->name('commissions.bulk-pay');

    // Withdrawal requests management
    Route::get('withdrawals', [AdminWithdrawalController::class, 'index'])->name('withdrawals.index');
    Route::get('withdrawals/{withdrawal}', [AdminWithdrawalController::class, 'show'])->name('withdrawals.show');
    Route::post('withdrawals/{withdrawal}/approve', [AdminWithdrawalController::class, 'approve'])->name('withdrawals.approve');
    Route::post('withdrawals/{withdrawal}/reject', [AdminWithdrawalController::class, 'reject'])->name('withdrawals.reject');
    Route::post('withdrawals/{withdrawal}/processing', [AdminWithdrawalController::class, 'markAsProcessing'])->name('withdrawals.processing');
    Route::post('withdrawals/{withdrawal}/complete', [AdminWithdrawalController::class, 'complete'])->name('withdrawals.complete');
    Route::post('withdrawals/bulk-approve', [AdminWithdrawalController::class, 'bulkApprove'])->name('withdrawals.bulk-approve');
    Route::get('withdrawals-export', [AdminWithdrawalController::class, 'export'])->name('withdrawals.export');

    // Telegram Bots management
    Route::resource('telegram-bots', App\Http\Controllers\Admin\TelegramBotController::class);
    Route::post('telegram-bots/{telegramBot}/toggle-status', [App\Http\Controllers\Admin\TelegramBotController::class, 'toggleStatus'])->name('telegram-bots.toggle-status');
    Route::post('telegram-bots/{telegramBot}/test', [App\Http\Controllers\Admin\TelegramBotController::class, 'test'])->name('telegram-bots.test');
    Route::post('telegram-bots/{telegramBot}/reset-webhook', [App\Http\Controllers\Admin\TelegramBotController::class, 'resetWebhook'])->name('telegram-bots.reset-webhook');
});

// Agent routes
Route::prefix('agent')->name('agent.')->middleware(['auth', 'agent'])->group(function () {
    Route::get('/dashboard', [AgentDashboardController::class, 'index'])->name('dashboard');
    Route::get('/referral-link', [AgentDashboardController::class, 'referralLink'])->name('referral-link');

    // Sales
    Route::get('sales', [AgentSalesController::class, 'index'])->name('sales.index');
    Route::get('sales/{purchase}', [AgentSalesController::class, 'show'])->name('sales.show');

    // Commissions
    Route::get('commissions', [AgentCommissionController::class, 'index'])->name('commissions.index');
    Route::get('commissions/{commission}', [AgentCommissionController::class, 'show'])->name('commissions.show');

    // Withdrawal requests
    Route::get('withdrawals', [AgentWithdrawalController::class, 'index'])->name('withdrawals.index');
    Route::get('withdrawals/create', [AgentWithdrawalController::class, 'create'])->name('withdrawals.create');
    Route::post('withdrawals', [AgentWithdrawalController::class, 'store'])->name('withdrawals.store');
    Route::get('withdrawals/{withdrawal}', [AgentWithdrawalController::class, 'show'])->name('withdrawals.show');
    Route::post('withdrawals/{withdrawal}/cancel', [AgentWithdrawalController::class, 'cancel'])->name('withdrawals.cancel');

    // Wallet settings
    Route::get('wallet-settings', [AgentWithdrawalController::class, 'walletSettings'])->name('wallet-settings');
    Route::post('wallet-settings', [AgentWithdrawalController::class, 'updateWalletSettings'])->name('wallet-settings.update');

    // Referral link
    Route::get('referral-link', function() {
        $agent = auth()->user()->agent;
        $referralLink = url('/register?ref=' . $agent->referral_code);
        return view('agent.referral-link', compact('agent', 'referralLink'));
    })->name('referral-link');

    // Telegram Bot management
    Route::prefix('telegram')->name('telegram.')->group(function () {
        Route::get('/', [App\Http\Controllers\Agent\TelegramBotController::class, 'index'])->name('index');
        Route::post('/', [App\Http\Controllers\Agent\TelegramBotController::class, 'store'])->name('store');
        Route::post('/toggle-status', [App\Http\Controllers\Agent\TelegramBotController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/test', [App\Http\Controllers\Agent\TelegramBotController::class, 'test'])->name('test');
        Route::get('/users', [App\Http\Controllers\Agent\TelegramBotController::class, 'users'])->name('users');
        Route::post('/send-message', [App\Http\Controllers\Agent\TelegramBotController::class, 'sendMessage'])->name('send-message');
        Route::post('/broadcast', [App\Http\Controllers\Agent\TelegramBotController::class, 'broadcast'])->name('broadcast');
    });
});


